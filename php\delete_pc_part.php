<?php
/**
 * 刪除電腦零件
 * KMS PC Receipt Maker
 */

require_once 'DatabaseMySQLi.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

// 只允許POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Response::error('只允許POST請求', 405);
}

try {
    // 獲取JSON數據
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data || !isset($data['id'])) {
        Response::error('無效的請求數據');
    }
    
    $partId = intval($data['id']);
    
    if ($partId <= 0) {
        Response::error('無效的零件ID', 400);
    }
    
    $db = new Database();
    
    // 檢查零件是否存在
    $checkSql = "SELECT id FROM pc_parts WHERE id = ?";
    $existing = $db->fetch($checkSql, [$partId]);
    
    if (!$existing) {
        Response::error('零件不存在', 404);
    }
    
    // 刪除零件
    $sql = "DELETE FROM pc_parts WHERE id = ?";
    $affectedRows = $db->delete($sql, [$partId]);
    
    if ($affectedRows > 0) {
        Response::success(['id' => $partId], '零件刪除成功');
    } else {
        Response::error('刪除零件失敗');
    }
    
} catch (Exception $e) {
    error_log('Delete PC part error: ' . $e->getMessage());
    Response::error('刪除零件失敗: ' . $e->getMessage());
}
?>