/**
 * PDF Generator Module
 * Handles PDF generation functionality for receipts
 */
class PDFGenerator {
    constructor() {
        this.isGenerating = false;
    }

    /**
     * Generate PDF Receipt - Using html2canvas for exact styling
     */
    async generatePDF(receiptData, receiptNumber) {
        if (this.isGenerating) {
            console.log('PDF generation already in progress');
            return;
        }

        this.isGenerating = true;

        try {
            // Show loading message
            if (typeof UIManager !== 'undefined' && UIManager.showMessage) {
                UIManager.showMessage('正在生成PDF，請稍候...', 'info');
            } else {
                console.log('正在生成PDF，請稍候...');
            }

            // Get the receipt preview element
            const receiptElement = document.getElementById('receiptPreview');
            if (!receiptElement || !receiptElement.querySelector('.receipt-container')) {
                throw new Error('Receipt preview not found. Please generate receipt first.');
            }

            // Import jsPDF and html2canvas if not already loaded
            if (typeof window.jspdf === 'undefined' && typeof window.jsPDF === 'undefined') {
                await this.loadScript('https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js');
            }
            if (typeof window.html2canvas === 'undefined' && typeof html2canvas === 'undefined') {
                await this.loadScript('https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js');
            }

            // Support both UMD globals: window.jspdf.jsPDF (preferred) and window.jsPDF.jsPDF (fallback)
            const jsPDFNamespace = window.jspdf || window.jsPDF;
            if (!jsPDFNamespace || !jsPDFNamespace.jsPDF) {
                throw new Error('jsPDF library failed to load');
            }
            const { jsPDF } = jsPDFNamespace;

            // Create a temporary container for PDF generation
            const tempContainer = document.createElement('div');
            tempContainer.style.position = 'absolute';
            tempContainer.style.left = '-9999px';
            tempContainer.style.top = '0';
            tempContainer.style.width = '816px'; // Letter width in pixels at 96 DPI (8.5in)
            tempContainer.style.height = 'auto';
            tempContainer.style.backgroundColor = 'white';
            tempContainer.style.padding = '0';
            tempContainer.style.margin = '0';
            tempContainer.style.fontFamily = 'Courier New, monospace';
            tempContainer.style.fontSize = '12pt';
            tempContainer.style.lineHeight = '1.4';
            document.body.appendChild(tempContainer);

            // Generate receipt HTML for PDF using the same paginated print logic
            const receiptHtml = (window.PrintManager && typeof window.PrintManager.generateReceiptHtmlForPrint === 'function')
                ? window.PrintManager.generateReceiptHtmlForPrint(receiptData)
                : (window.ReceiptGenerator ? window.ReceiptGenerator.generateReceiptHtml(receiptData) : receiptElement.innerHTML);
            tempContainer.innerHTML = receiptHtml;

            // Apply exact Receipt Preview styles for PDF generation
            const style = document.createElement('style');
            style.textContent = `
                body {
                    margin: 0;
                    padding: 0;
                    background: white;
                    font-family: 'Courier New', monospace;
                }

                /* Main Receipt Container with Elegant Certificate Border - PDF Optimized */
                .receipt-container {
                    position: relative !important;
                    width: 8.5in !important;
                    height: 11in !important;
                    max-width: 8.5in !important;
                    margin: 0 auto !important;
                    padding: 2rem 2.5rem !important;
                    font-family: 'Courier New', monospace !important;
                    font-size: 12pt !important;
                    line-height: 1.4 !important;
                    background: white !important;
                    border-radius: 60px !important;
                    box-shadow:
                        0 0 0 3px #D4AF37,
                        0 0 0 6px white,
                        0 0 0 9px #B8860B,
                        0 0 0 12px white,
                        0 0 0 15px #DAA520,
                        0 8px 30px rgba(0, 0, 0, 0.2),
                        inset 0 0 0 2px #F5DEB3 !important;
                    background-image:
                        radial-gradient(circle at 20px 20px, #F5DEB3 2px, transparent 2px),
                        radial-gradient(circle at 60px 60px, #F5DEB3 1px, transparent 1px) !important;
                    background-size: 80px 80px, 40px 40px !important;
                    background-position: 0 0, 20px 20px !important;
                    box-sizing: border-box !important;
                    display: flex !important;
                    flex-direction: column !important;
                }

                .receipt-container::before {
                    content: '' !important;
                    position: absolute !important;
                    z-index: -1 !important;
                    top: 20px !important;
                    left: 20px !important;
                    right: 20px !important;
                    bottom: 20px !important;
                    border: 1px solid #D4AF37 !important;
                    border-radius: 48px !important;
                    background: linear-gradient(45deg,
                        transparent 0%,
                        rgba(212, 175, 55, 0.1) 25%,
                        transparent 50%,
                        rgba(212, 175, 55, 0.1) 75%,
                        transparent 100%) !important;
                }

                .receipt-container::after {
                    content: '' !important;
                    position: absolute !important;
                    z-index: -1 !important;
                    top: 30px !important;
                    left: 30px !important;
                    right: 30px !important;
                    bottom: 30px !important;
                    display: none !important;
                }

                .receipt-inner {
                    position: relative !important;
                    z-index: 1 !important;
                    flex: 1 1 auto !important;
                    display: flex !important;
                    flex-direction: column !important;
                }

                .receipt-bottom-fixed {
                    margin-top: auto !important;
                }

                /* Receipt Header */
                .preview-receipt-header {
                    text-align: center !important;
                    border-bottom: 2px solid #333 !important;
                    padding-bottom: 0.5rem !important;
                    margin-bottom: 0.75rem !important;
                }

                .receipt-title {
                    font-size: 1.8rem !important;
                    font-weight: bold !important;
                    color: #333 !important;
                    margin-bottom: 0.5rem !important;
                }

                .receipt-company-info {
                    font-size: 0.9rem !important;
                    color: #666 !important;
                    line-height: 1.3 !important;
                }

                /* Customer Info */
                .preview-customer-info {
                    background-color: #8efdeb !important;
                    padding: 0.75rem !important;
                    border-radius: 8px !important;
                    margin-bottom: 0.75rem !important;
                }

                /* Receipt Table */
                .receipt-table {
                    width: 100% !important;
                    border-collapse: collapse !important;
                    margin-bottom: 0.75rem !important;
                }

                .receipt-table thead tr {
                    background-color: #81c0ff !important;
                }

                .receipt-table th, .receipt-table td {
                    padding: 3px 4px !important;
                    border: 1px solid #ddd !important;
                    font-size: 11px !important;
                    text-align: left !important;
                }

                /* Payment Method */
                .payment-method {
                    background-color: #f8f9fa !important;
                    padding: 0.75rem !important;
                    border: 1px solid #dee2e6 !important;
                    border-radius: 8px !important;
                    margin-top: 0.75rem !important;
                }

                .payment-method-title {
                    margin-bottom: 0.75rem !important;
                    font-size: 1.1rem !important;
                    font-weight: bold !important;
                    color: #333 !important;
                }

                .payment-options {
                    display: flex !important;
                    flex-wrap: wrap !important;
                    gap: 0.5rem !important;
                }

                .payment-option-button {
                    display: flex !important;
                    align-items: center !important;
                    padding: 0.25rem 0.5rem !important;
                    border: 2px solid #D4AF37 !important;
                    border-radius: 12px !important;
                    background: white !important;
                    font-size: 0.9rem !important;
                }

                .payment-checkbox {
                    width: 12px !important;
                    height: 12px !important;
                    border: 1px solid #333 !important;
                    margin-right: 0.25rem !important;
                    background: white !important;
                }

                /* Signature Section */
                .signature-section {
                    margin-top: 1.5rem !important;
                    padding-top: 1rem !important;
                    font-size: 1rem !important;
                    text-align: left !important;
                    color: #333 !important;
                }

                .signature-labels-row {
                    display: flex !important;
                    justify-content: space-between !important;
                    margin-bottom: 0.5rem !important;
                }

                .signature-label-item {
                    flex: 1 !important;
                    text-align: center !important;
                }

                .signature-label {
                    font-size: 0.9rem !important;
                    color: #666 !important;
                }

                .signature-lines-area {
                    height: 2rem !important;
                    border-bottom: 1px solid #333 !important;
                    margin-top: 1rem !important;
                }
            `;
            tempContainer.appendChild(style);

            // Wait for images (logo) to load and fonts to render
            await new Promise((resolve) => setTimeout(resolve, 500));

            // Render each page container separately to match Print layout exactly
            const pageNodes = Array.from(tempContainer.querySelectorAll('.receipt-container'));
            if (pageNodes.length === 0) {
                throw new Error('No receipt pages found for PDF generation');
            }

            // Ensure html-to-image library is loaded
            if (!window.htmlToImage) {
                await this.loadScript('https://cdnjs.cloudflare.com/ajax/libs/html-to-image/1.11.11/html-to-image.min.js');
                if (!window.htmlToImage) {
                    throw new Error('Failed to load html-to-image library');
                }
            }
            const htmlToImage = window.htmlToImage;
            const canvases = [];

            for (let i = 0; i < pageNodes.length; i++) {
                const node = pageNodes[i];

                // Force exact Letter page dimensions with proper margins for borders
                node.style.width = '8.5in';
                node.style.height = '11in';
                node.style.minHeight = '11in';
                node.style.maxHeight = '11in';
                node.style.boxSizing = 'border-box';
                node.style.overflow = 'visible';
                node.style.position = 'relative';
                // 移除內邊距，讓容器自然顯示
                node.style.padding = '0';
                node.style.margin = '0';
                
                // 修改receipt-container樣式，保持邊框完整顯示
                const receiptContainers = node.querySelectorAll('.receipt-container');
                receiptContainers.forEach(container => {
                    // 保留適當的邊距以確保邊框完整顯示
                    container.style.margin = '0 auto';
                    container.style.padding = '2rem 2.5rem';
                    // 保持圓角效果
                    container.style.borderRadius = '60px';
                    // 恢復完整的邊框效果
                    container.style.boxShadow = `
                        0 0 0 3px #D4AF37,
                        0 0 0 6px white,
                        0 0 0 9px #B8860B,
                        0 0 0 12px white,
                        0 0 0 15px #DAA520,
                        0 8px 30px rgba(0, 0, 0, 0.2),
                        inset 0 0 0 2px #F5DEB3
                    `;
                    // 移除直接邊框，使用 box-shadow 來創建邊框效果
                    container.style.border = 'none';
                    // 恢復原始尺寸
                    container.style.width = '8.5in';
                    container.style.maxWidth = '8.5in';
                    container.style.height = '11in';
                    container.style.maxHeight = '11in';
                });

                // Wait a bit more for layout to settle
                await new Promise((resolve) => setTimeout(resolve, 100));

                // 增加 Canvas 尺寸以捕獲 box-shadow 邊框
                const extraSpace = 15; // 減少額外空間，讓邊框更貼近內容
                const canvas = await htmlToImage.toCanvas(node, {
                    pixelRatio: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: 'white',
                    // 增加尺寸以捕獲邊框
                    width: node.offsetWidth + (extraSpace * 2),
                    height: node.offsetHeight + (extraSpace * 2),
                    style: {
                        transform: `translate(${extraSpace}px, ${extraSpace}px)`,
                        transformOrigin: 'top left'
                    }
                });
                canvases.push(canvas);
            }

            // Create PDF with Letter size
            const pdf = new jsPDF('p', 'pt', 'letter');
            const pageWidthPt = pdf.internal.pageSize.getWidth();
            const pageHeightPt = pdf.internal.pageSize.getHeight();

            canvases.forEach((cv, idx) => {
                if (idx > 0) pdf.addPage('letter', 'p');
                const imgData = cv.toDataURL('image/png');
                // 保持原始尺寸，不縮小內容
                pdf.addImage(imgData, 'PNG', 0, 0, pageWidthPt, pageHeightPt);
            });

            // Remove temporary container
            document.body.removeChild(tempContainer);

            // Save the PDF
            const filename = `Receipt_${receiptNumber || 'KMS-UltraVIP-0000001'}_${new Date().toISOString().slice(0, 10)}.pdf`;
            pdf.save(filename);

            if (typeof UIManager !== 'undefined' && UIManager.showMessage) {
                UIManager.showMessage('PDF已成功生成並下載', 'success');
            } else {
                console.log('PDF已成功生成並下載');
            }

        } catch (error) {
            console.error('PDF generation error:', error, error.stack);
            if (typeof UIManager !== 'undefined' && UIManager.showMessage) {
                UIManager.showMessage('PDF生成失敗: ' + error.message, 'error');
            } else {
                alert('PDF生成失敗: ' + error.message);
            }
        } finally {
            this.isGenerating = false;
        }
    }

    /**
     * Load external script dynamically
     */
    loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }
}

// Create global instance
window.PDFGenerator = new PDFGenerator();
