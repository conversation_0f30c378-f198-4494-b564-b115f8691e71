/**
 * UI Manager
 * Handles UI updates, section switching, message display, and form management
 */

class UIManager {
    constructor() {
        this.currentSection = 'create';
    }

    /**
     * Show Message
     */
    showMessage(message, type = 'info', duration = 3000) {
        // Create message element
        const messageDiv = document.createElement('div');
        messageDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show toast-message`;

        messageDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(messageDiv);

        // Auto remove
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.classList.add('removing');
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.remove();
                    }
                }, 300);
            }
        }, duration);
    }

    /**
     * Show Section
     */
    showSection(sectionName) {
        // Hide all sections and remove active class
        const sections = document.querySelectorAll('.section');
        sections.forEach(section => {
            section.classList.remove('active');
            section.style.display = 'none';
        });

        // Show specified section
        const targetSection = document.getElementById(sectionName + 'Section');
        if (targetSection) {
            targetSection.classList.add('active');
            targetSection.style.display = 'block';
            this.currentSection = sectionName;

            // If it's history section, load receipt list
            if (sectionName === 'history') {
                this.loadReceiptHistory();
            }
        }

        // Update navigation state
        const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
        });

        // Set current link as active
        const activeLink = document.querySelector(`[onclick="showSection('${sectionName}')"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }

    /**
     * Clear Form
     */
    clearForm() {
        if (confirm('Are you sure you want to clear the entire form? This will remove all entered data.')) {
            // Clear customer information
            const customerName = document.getElementById('customerName');
            const customerPhone = document.getElementById('customerPhone');
            const customerEmail = document.getElementById('customerEmail');
            const customerAddress = document.getElementById('customerAddress');
            const notes = document.getElementById('notes');
            const discountAmount = document.getElementById('discountAmount');
            const taxRate = document.getElementById('taxRate');

            if (customerName) customerName.value = '';
            if (customerPhone) customerPhone.value = '';
            if (customerEmail) customerEmail.value = '';
            if (customerAddress) customerAddress.value = '';
            if (notes) notes.value = '';
            if (discountAmount) discountAmount.value = '0';
            if (taxRate) taxRate.value = '0';

            // Clear items
            if (window.ItemManager) {
                window.ItemManager.setReceiptItems([]);
            }

            // Regenerate receipt number
            if (window.AppInitializer) {
                window.AppInitializer.generateReceiptNumber();
                // Date field removed - no longer needed
            }

            this.showMessage('Form cleared', 'info');
        }
    }

    /**
     * Load Receipt History
     */
    async loadReceiptHistory() {
        try {
            const response = await fetch('php/get_receipts.php');
            const data = await response.json();

            if (data.success) {
                this.displayReceiptHistory(data.data.receipts);
            } else {
                console.error('Failed to load receipt history:', data.message);
                this.showMessage('Failed to load history', 'error');
            }
        } catch (error) {
            console.error('Error loading receipt history:', error);
            this.showMessage('Error occurred while loading history', 'error');
        }
    }

    /**
     * Display Receipt History
     */
    displayReceiptHistory(receipts) {
        // Use the enhanced version with batch operations
        this.displayReceiptHistoryWithBatch(receipts);
    }

    /**
            <div class="receipt-history-item border rounded p-3 mb-3">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <h6 class="mb-1">${receipt.receipt_number}</h6>
                        <small class="text-muted">${new Date(receipt.created_at).toLocaleDateString('en-US')}</small>
                    </div>
                    <div class="col-md-3">
                        <strong>${receipt.customer_name}</strong>
                        ${receipt.customer_phone ? `<br><small class="text-muted">${receipt.customer_phone}</small>` : ''}
                    </div>
                    <div class="col-md-2">
                        <span class="badge bg-primary">${receipt.payment_method}</span>
                    </div>
                    <div class="col-md-2">
                        <div class="fw-bold">$${parseFloat(receipt.total_amount).toFixed(2)}</div>
                    </div>
                    <div class="col-md-2">
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-outline-primary"
                                    onclick="viewReceiptDetails(${receipt.id})"
                                    title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger"
                                    onclick="deleteReceipt(${receipt.id})"
                                    title="Delete Receipt">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * View Receipt Details
     */
    async viewReceiptDetails(receiptId) {
    try {
        const response = await fetch(`php/get_receipt.php?id=${receiptId}`);
        const data = await response.json();

        if (data.success) {
            this.showReceiptDetailsModal(data.data);
        } else {
            this.showMessage('Failed to load receipt details', 'error');
        }
    } catch (error) {
        console.error('Error loading receipt details:', error);
        this.showMessage('Error occurred while loading receipt details', 'error');
    }
}

/**
 * Show Receipt Details Modal
 */
showReceiptDetailsModal(receipt) {
    // Create modal HTML
    const modalHtml = `
            <div class="modal fade" id="receiptDetailsModal" tabindex="-1" aria-labelledby="receiptDetailsModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="receiptDetailsModalLabel">
                                <i class="fas fa-receipt me-2"></i>
                                Receipt Details - ${receipt.receipt_number}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <!-- Customer Information -->
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">
                                                <i class="fas fa-user me-2"></i>
                                                Customer Information
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-sm-4"><strong>Name:</strong></div>
                                                <div class="col-sm-8">${receipt.customer_name || 'N/A'}</div>
                                            </div>
                                            <hr class="my-2">
                                            <div class="row">
                                                <div class="col-sm-4"><strong>Phone:</strong></div>
                                                <div class="col-sm-8">${receipt.customer_phone || 'N/A'}</div>
                                            </div>
                                            <hr class="my-2">
                                            <div class="row">
                                                <div class="col-sm-4"><strong>Email:</strong></div>
                                                <div class="col-sm-8">${receipt.customer_email || 'N/A'}</div>
                                            </div>
                                            <hr class="my-2">
                                            <div class="row">
                                                <div class="col-sm-4"><strong>Address:</strong></div>
                                                <div class="col-sm-8">${receipt.customer_address || 'N/A'}</div>
                                            </div>
                                            ${receipt.notes ? `
                                            <hr class="my-2">
                                            <div class="row">
                                                <div class="col-sm-4"><strong>Notes:</strong></div>
                                                <div class="col-sm-8">${receipt.notes}</div>
                                            </div>
                                            ` : ''}
                                        </div>
                                    </div>
                                </div>

                                <!-- Receipt Information -->
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">
                                                <i class="fas fa-file-invoice me-2"></i>
                                                Receipt Information
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-sm-4"><strong>Receipt #:</strong></div>
                                                <div class="col-sm-8">${receipt.receipt_number}</div>
                                            </div>
                                            <hr class="my-2">
                                            <div class="row">
                                                <div class="col-sm-4"><strong>Date:</strong></div>
                                                <div class="col-sm-8">${new Date(receipt.created_at).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    })}</div>
                                            </div>
                                            <hr class="my-2">
                                            <div class="row">
                                                <div class="col-sm-4"><strong>Payment:</strong></div>
                                                <div class="col-sm-8">
                                                    <span class="badge bg-primary">${receipt.payment_method || 'cash'}</span>
                                                </div>
                                            </div>
                                            <hr class="my-2">
                                            <div class="row">
                                                <div class="col-sm-4"><strong>Subtotal:</strong></div>
                                                <div class="col-sm-8">$${parseFloat(receipt.subtotal || 0).toFixed(2)}</div>
                                            </div>
                                            ${receipt.discount_amount > 0 ? `
                                            <hr class="my-2">
                                            <div class="row">
                                                <div class="col-sm-4"><strong>Discount:</strong></div>
                                                <div class="col-sm-8 text-success">-$${parseFloat(receipt.discount_amount).toFixed(2)}</div>
                                            </div>
                                            ` : ''}
                                            ${receipt.tax_amount > 0 ? `
                                            <hr class="my-2">
                                            <div class="row">
                                                <div class="col-sm-4"><strong>Tax:</strong></div>
                                                <div class="col-sm-8">$${parseFloat(receipt.tax_amount).toFixed(2)}</div>
                                            </div>
                                            ` : ''}
                                            <hr class="my-2">
                                            <div class="row">
                                                <div class="col-sm-4"><strong>Total:</strong></div>
                                                <div class="col-sm-8"><strong class="text-primary">$${parseFloat(receipt.total_amount).toFixed(2)}</strong></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- PC Parts/Items -->
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-microchip me-2"></i>
                                        PC Parts & Items (${receipt.items ? receipt.items.length : 0} items)
                                    </h6>
                                </div>
                                <div class="card-body">
                                    ${receipt.items && receipt.items.length > 0 ? `
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Item</th>
                                                        <th>Category</th>
                                                        <th>Description</th>
                                                        <th class="text-center">Qty</th>
                                                        <th class="text-end">Unit Price</th>
                                                        <th class="text-end">Total</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    ${receipt.items.map(item => `
                                                        <tr>
                                                            <td><strong>${item.item_name}</strong></td>
                                                            <td>
                                                                ${item.category ? `<span class="badge bg-secondary">${item.category}</span>` : 'N/A'}
                                                            </td>
                                                            <td>${item.item_description || 'N/A'}</td>
                                                            <td class="text-center">${item.quantity}</td>
                                                            <td class="text-end">$${parseFloat(item.unit_price).toFixed(2)}</td>
                                                            <td class="text-end"><strong>$${parseFloat(item.total_price).toFixed(2)}</strong></td>
                                                        </tr>
                                                    `).join('')}
                                                </tbody>
                                            </table>
                                        </div>
                                    ` : `
                                        <div class="text-center text-muted py-4">
                                            <i class="fas fa-inbox fa-2x mb-2"></i>
                                            <p>No items found in this receipt</p>
                                        </div>
                                    `}
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary" onclick="editReceiptDetails(${receipt.id})">
                                <i class="fas fa-edit me-1"></i>
                                Edit Receipt
                            </button>
                            <button type="button" class="btn btn-info" onclick="printReceiptDetails(${receipt.id})">
                                <i class="fas fa-print me-1"></i>
                                Print Receipt
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

    // Remove existing modal if present
    const existingModal = document.getElementById('receiptDetailsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('receiptDetailsModal'));
    modal.show();

    // Clean up modal when hidden
    document.getElementById('receiptDetailsModal').addEventListener('hidden.bs.modal', function () {
        this.remove();
    });
}

    /**
     * Edit Receipt Details
     */
    async editReceiptDetails(receiptId) {
    try {
        const response = await fetch(`php/get_receipt.php?id=${receiptId}`);
        const data = await response.json();

        if (data.success) {
            const receipt = data.data;

            // Close the details modal
            const detailsModal = bootstrap.Modal.getInstance(document.getElementById('receiptDetailsModal'));
            if (detailsModal) {
                detailsModal.hide();
            }

            // Switch to create section
            this.showSection('create');

            // Populate form with receipt data
            this.populateFormWithReceiptData(receipt);

            this.showMessage('Receipt loaded for editing', 'info');
        } else {
            this.showMessage('Failed to load receipt for editing', 'error');
        }
    } catch (error) {
        console.error('Error loading receipt for editing:', error);
        this.showMessage('Error occurred while loading receipt for editing', 'error');
    }
}

/**
 * Populate Form with Receipt Data
 */
populateFormWithReceiptData(receipt) {
    // Populate customer information
    const customerName = document.getElementById('customerName');
    const customerPhone = document.getElementById('customerPhone');
    const customerEmail = document.getElementById('customerEmail');
    const customerAddress = document.getElementById('customerAddress');
    const notes = document.getElementById('notes');
    const receiptNumber = document.getElementById('receiptNumber');

    if (customerName) customerName.value = receipt.customer_name || '';
    if (customerPhone) customerPhone.value = receipt.customer_phone || '';
    if (customerEmail) customerEmail.value = receipt.customer_email || '';
    if (customerAddress) customerAddress.value = receipt.customer_address || '';
    if (notes) notes.value = receipt.notes || '';
    if (receiptNumber) receiptNumber.value = receipt.receipt_number || '';

    // Populate items if ItemManager is available
    if (window.ItemManager && receipt.items) {
        // Clear existing items
        window.ItemManager.setReceiptItems([]);

        // Add receipt items with correct price structure
        receipt.items.forEach(item => {
            const receiptItem = {
                id: Date.now() + Math.random(), // Generate temporary ID
                name: item.item_name,
                description: item.item_description || '',
                category: item.category || '',
                quantity: parseInt(item.quantity),
                unitPrice: parseFloat(item.unit_price),
                totalPrice: parseFloat(item.total_price),
                originalPrice: parseFloat(item.unit_price), // Use unit_price as original
                specialPrice: parseFloat(item.unit_price), // Use unit_price as special
                discountPercent: 0,
                hidePrice: false
            };

            // Directly add to receiptItems array to avoid re-processing
            window.ItemManager.receiptItems.push(receiptItem);
        });

        // Update display after all items are added
        window.ItemManager.updateReceiptItemsDisplay();
        window.ItemManager.updateTotals();
    }

    // Set discount and tax if available
    const discountAmount = document.getElementById('discountAmount');
    const taxRate = document.getElementById('taxRate');

    if (discountAmount && receipt.discount_amount) {
        discountAmount.value = parseFloat(receipt.discount_amount);
    }

    if (taxRate && receipt.tax_amount && receipt.subtotal) {
        const taxRatePercent = (parseFloat(receipt.tax_amount) / parseFloat(receipt.subtotal)) * 100;
        taxRate.value = taxRatePercent.toFixed(1);
    }
}

/**
 * Print Receipt Details
 */
printReceiptDetails(receiptId) {
    // Get the modal content
    const modalContent = document.querySelector('#receiptDetailsModal .modal-body');
    if (!modalContent) {
        this.showMessage('Unable to print receipt', 'error');
        return;
    }

    // Create a new window for printing
    const printWindow = window.open('', '_blank');

    const printContent = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Receipt ${receiptId}</title>
                <link href="css/bootstrap.min.css" rel="stylesheet">
                <style>
                    body { font-family: Arial, sans-serif; }
                    .card { border: 1px solid #ddd; margin-bottom: 20px; }
                    .card-header { background-color: #f8f9fa; padding: 10px; font-weight: bold; }
                    .card-body { padding: 15px; }
                    .badge { padding: 4px 8px; border-radius: 4px; }
                    .bg-primary { background-color: #0d6efd !important; color: white; }
                    .bg-secondary { background-color: #6c757d !important; color: white; }
                    .text-primary { color: #0d6efd !important; }
                    .text-success { color: #198754 !important; }
                    .table { width: 100%; border-collapse: collapse; }
                    .table th, .table td { padding: 8px; border: 1px solid #ddd; }
                    .table-striped tbody tr:nth-of-type(odd) { background-color: #f9f9f9; }
                    .text-center { text-align: center; }
                    .text-end { text-align: right; }
                    @media print {
                        .no-print { display: none; }
                    }
                </style>
            </head>
            <body>
                ${modalContent.innerHTML}
            </body>
            </html>
        `;

    printWindow.document.write(printContent);
    printWindow.document.close();

    // Wait for content to load then print
    printWindow.onload = function () {
        printWindow.print();
        printWindow.close();
    };
}

    /**
     * Search Receipts
     */
    async searchReceipts() {
    const searchTerm = document.getElementById('searchInput')?.value.trim() || '';
    const paymentMethod = document.getElementById('paymentFilter')?.value || '';
    const dateFrom = document.getElementById('dateFromFilter')?.value || '';
    const dateTo = document.getElementById('dateToFilter')?.value || '';

    const params = new URLSearchParams();
    if (searchTerm) params.append('search', searchTerm);
    if (paymentMethod) params.append('payment_method', paymentMethod);
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);

    try {
        const response = await fetch(`php/get_receipts.php?${params.toString()}`);
        const data = await response.json();

        if (data.success) {
            this.displayReceiptHistory(data.data.receipts);
        } else {
            this.showMessage('Search failed', 'error');
        }
    } catch (error) {
        console.error('Error searching receipts:', error);
        this.showMessage('Error occurred during search', 'error');
    }
}

/**
 * Clear Filters
 */
clearFilters() {
    const searchInput = document.getElementById('searchInput');
    const paymentFilter = document.getElementById('paymentFilter');
    const dateFromFilter = document.getElementById('dateFromFilter');
    const dateToFilter = document.getElementById('dateToFilter');

    if (searchInput) searchInput.value = '';
    if (paymentFilter) paymentFilter.value = '';
    if (dateFromFilter) dateFromFilter.value = '';
    if (dateToFilter) dateToFilter.value = '';

    this.loadReceiptHistory();
}

/**
 * Handle Search Key Press
 */
handleSearchKeyPress(event) {
    if (event.key === 'Enter') {
        this.searchReceipts();
    }
}

/**
 * Show Preset Modal
 */
showPresetModal() {
    // Wait a bit for presetManager to load if it's not ready yet
    if (typeof window.presetManager === 'undefined') {
        setTimeout(() => {
            this.showPresetModal();
        }, 100);
        return;
    }

    // Check if presetManager exists and has the method
    if (window.presetManager && typeof window.presetManager.showPresetModal === 'function') {
        window.presetManager.showPresetModal();
    } else {
        // Fallback implementation using pure CSS modal
        const modal = document.getElementById('presetModal');
        if (modal) {
            modal.classList.add('is-open');
            modal.setAttribute('aria-hidden', 'false');
        } else {
            const message = window.LanguageManager ?
                window.LanguageManager.getText('preset_feature_unavailable') :
                'Preset items feature is temporarily unavailable';
            this.showMessage(message, 'warning');
        }
    }
}

/**
 * Show Configuration Modal
 */
showConfigurationModal() {
    // Check if configManager exists
    if (typeof window.configManager !== 'undefined' && window.configManager.showConfigurationModal) {
        window.configManager.showConfigurationModal();
    } else {
        this.showMessage('Configuration management feature is temporarily unavailable', 'warning');
    }
}

/**
 * Save Receipt Configuration
 */
saveReceiptConfiguration() {
    // Check if configManager exists
    if (typeof window.configManager !== 'undefined' && window.configManager.saveReceiptConfiguration) {
        window.configManager.saveReceiptConfiguration();
    } else {
        this.showMessage('Configuration save feature is temporarily unavailable', 'warning');
    }
}

/**
 * Toggle Compact View
 */
toggleCompactView() {
    if (window.ItemManager) {
        window.ItemManager.toggleCompactView();
        this.showMessage('View mode toggled', 'info');
    }
}

/**
 * Toggle Drag and Drop
 */
toggleDragAndDrop() {
    if (window.ItemManager) {
        window.ItemManager.toggleDragAndDrop();
        const isEnabled = window.ItemManager.isDragDropEnabled;
        this.showMessage(`Drag and drop ${isEnabled ? 'enabled' : 'disabled'}`, 'info');
    }
}

    /**
     * Enhanced Display Receipt History with Batch Operations
     */
    displayReceiptHistoryWithBatch(receipts) {
        const container = document.getElementById('receiptHistory');
        if (!container) return;

        if (!receipts || receipts.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>No receipt records found</p>
                </div>
            `;
            return;
        }

        // Add batch delete controls
        const batchControls = `
            <div class="batch-controls mb-3 p-3 bg-light rounded" style="display: none;" id="batchControls">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <span id="selectedCount">0</span> receipts selected
                    </div>
                    <div>
                        <button class="btn btn-danger btn-sm" onclick="deleteSelectedReceipts()">
                            <i class="fas fa-trash me-1"></i>
                            Delete Selected
                        </button>
                        <button class="btn btn-secondary btn-sm" onclick="clearSelection()">
                            Clear Selection
                        </button>
                    </div>
                </div>
            </div>
        `;

        const receiptItems = receipts.map(receipt => `
            <div class="receipt-history-item border rounded p-3 mb-3" data-receipt-id="${receipt.id}">
                <div class="row align-items-center">
                    <div class="col-md-1">
                        <input type="checkbox" class="form-check-input receipt-checkbox" 
                               value="${receipt.id}" onchange="updateBatchControls()">
                    </div>
                    <div class="col-md-2">
                        <h6 class="mb-1">${receipt.receipt_number}</h6>
                        <small class="text-muted">${new Date(receipt.created_at).toLocaleDateString('en-US')}</small>
                    </div>
                    <div class="col-md-3">
                        <strong>${receipt.customer_name}</strong>
                        ${receipt.customer_phone ? `<br><small class="text-muted">${receipt.customer_phone}</small>` : ''}
                    </div>
                    <div class="col-md-2">
                        <span class="badge bg-primary">${receipt.payment_method}</span>
                    </div>
                    <div class="col-md-2">
                        <div class="fw-bold">$${parseFloat(receipt.total_amount).toFixed(2)}</div>
                    </div>
                    <div class="col-md-2">
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-outline-primary"
                                    onclick="viewReceiptDetails(${receipt.id})"
                                    title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger"
                                    onclick="deleteReceipt(${receipt.id})"
                                    title="Delete Receipt">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = batchControls + receiptItems;
    }

    /**
     * Get current section
     */
    getCurrentSection() {
        return this.currentSection;
    }



/**
 * Initialize UI components
 */
initializeUI() {
    // Set up any additional UI components
    this.setupTooltips();
    this.setupKeyboardShortcuts();
}

/**
 * Setup Tooltips
 */
setupTooltips() {
    // Initialize Bootstrap tooltips if available
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

/**
 * Setup Keyboard Shortcuts
 */
setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Ctrl+S to save receipt
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            if (window.ReceiptGenerator) {
                window.ReceiptGenerator.saveReceipt();
            }
        }

        // Ctrl+P to print receipt
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            if (window.ReceiptGenerator) {
                window.ReceiptGenerator.printReceipt();
            }
        }

        // Ctrl+N to clear form
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            this.clearForm();
        }
    });
}
}

// Create global instance
window.UIManager = new UIManager();

// Initialize UI when DOM is ready
document.addEventListener('DOMContentLoaded', function () {
    window.UIManager.initializeUI();
});

// Export functions for backward compatibility
window.showMessage = (message, type, duration) => window.UIManager.showMessage(message, type, duration);
window.showSection = (sectionName) => window.UIManager.showSection(sectionName);
window.clearForm = () => window.UIManager.clearForm();
window.viewReceiptDetails = (receiptId) => window.UIManager.viewReceiptDetails(receiptId);
window.editReceiptDetails = (receiptId) => window.UIManager.editReceiptDetails(receiptId);
window.printReceiptDetails = (receiptId) => window.UIManager.printReceiptDetails(receiptId);
window.searchReceipts = () => window.UIManager.searchReceipts();
window.clearFilters = () => window.UIManager.clearFilters();
window.handleSearchKeyPress = (event) => window.UIManager.handleSearchKeyPress(event);
window.showPresetModal = () => window.UIManager.showPresetModal();
window.showConfigurationModal = () => window.UIManager.showConfigurationModal();
window.saveReceiptConfiguration = () => window.UIManager.saveReceiptConfiguration();
