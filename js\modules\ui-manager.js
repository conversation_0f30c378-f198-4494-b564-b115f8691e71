/**
 * UI Manager
 * Handles UI updates, section switching, message display, and form management
 */

class UIManager {
    constructor() {
        this.currentSection = 'create';
    }

    /**
     * Show Message
     */
    showMessage(message, type = 'info', duration = 3000) {
        // Create message element
        const messageDiv = document.createElement('div');
        messageDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show toast-message`;
        
        messageDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(messageDiv);
        
        // Auto remove
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.classList.add('removing');
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.remove();
                    }
                }, 300);
            }
        }, duration);
    }

    /**
     * Show Section
     */
    showSection(sectionName) {
        console.log(`UIManager.showSection called with: ${sectionName}`);
        
        // Hide all sections and remove active class
        const sections = document.querySelectorAll('.section');
        sections.forEach(section => {
            section.classList.remove('active');
            section.style.display = 'none';
        });

        // Show specified section
        const targetSection = document.getElementById(sectionName + 'Section');
        if (targetSection) {
            targetSection.classList.add('active');
            targetSection.style.display = 'block';
            this.currentSection = sectionName;
            
            console.log(`${sectionName}Section is now visible`);

            // If it's history section, load receipt list
            if (sectionName === 'history') {
                this.loadReceiptHistory();
            }
        } else {
            console.error(`${sectionName}Section not found`);
        }

        // Update navigation state
        const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
        });

        // Set current link as active
        const activeLink = document.querySelector(`[onclick="showSection('${sectionName}')"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }

    /**
     * Clear Form
     */
    clearForm() {
        if (confirm('Are you sure you want to clear the entire form? This will remove all entered data.')) {
            // Clear customer information
            const customerName = document.getElementById('customerName');
            const customerPhone = document.getElementById('customerPhone');
            const customerEmail = document.getElementById('customerEmail');
            const customerAddress = document.getElementById('customerAddress');
            const notes = document.getElementById('notes');
            const discountAmount = document.getElementById('discountAmount');
            const taxRate = document.getElementById('taxRate');

            if (customerName) customerName.value = '';
            if (customerPhone) customerPhone.value = '';
            if (customerEmail) customerEmail.value = '';
            if (customerAddress) customerAddress.value = '';
            if (notes) notes.value = '';
            if (discountAmount) discountAmount.value = '0';
            if (taxRate) taxRate.value = '0';

            // Clear items
            if (window.ItemManager) {
                window.ItemManager.setReceiptItems([]);
            }

            // Regenerate receipt number
            if (window.AppInitializer) {
                window.AppInitializer.generateReceiptNumber();
            }

            this.showMessage('Form cleared', 'info');
        }
    }

    /**
     * Load Receipt History
     */
    async loadReceiptHistory() {
        console.log('Loading receipt history...');
        try {
            const response = await fetch('php/get_receipts.php');
            const data = await response.json();

            if (data.success) {
                console.log('Receipt history loaded successfully');
                this.displayReceiptHistory(data.data.receipts);
            } else {
                console.error('Failed to load receipt history:', data.message);
                this.showMessage('Failed to load history', 'error');
            }
        } catch (error) {
            console.error('Error loading receipt history:', error);
            this.showMessage('Error occurred while loading history', 'error');
        }
    }

    /**
     * Display Receipt History
     */
    displayReceiptHistory(receipts) {
        console.log('Displaying receipt history with', receipts ? receipts.length : 0, 'receipts');
        const container = document.getElementById('receiptHistory');
        if (!container) {
            console.error('receiptHistory container not found');
            return;
        }

        if (!receipts || receipts.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-4">
                    <span class="icon-symbol extra-large">📦</span>
                    <p>No receipt records found</p>
                </div>
            `;
            return;
        }

        // Add batch delete controls
        const batchControls = `
            <div class="batch-controls bg-light rounded" style="display: none;" id="batchControls">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <span id="selectedCount">0</span> receipts selected
                    </div>
                    <div>
                        <button class="btn btn-danger btn-sm" onclick="deleteSelectedReceipts()">
                            <i class="fas fa-trash me-1"></i>
                            Delete Selected
                        </button>
                        <button class="btn btn-secondary btn-sm" onclick="clearSelection()">
                            Clear Selection
                        </button>
                    </div>
                </div>
            </div>
        `;

        const receiptItems = receipts.map(receipt => `
            <div class="receipt-history-item border rounded" data-receipt-id="${receipt.id}">
                <div class="row align-items-center">
                    <div class="col-md-1">
                        <input type="checkbox" class="form-check-input receipt-checkbox" 
                               value="${receipt.id}" onchange="updateBatchControls()">
                    </div>
                    <div class="col-md-3">
                        <h4 class="mb-1">${receipt.receipt_number}</h4>
                        <small class="text-muted">${new Date(receipt.created_at).toLocaleDateString('en-US')}</small>
                    </div>
                    <div class="col-md-3">
                        <strong>${receipt.customer_name}</strong>
                        ${receipt.customer_phone ? `<br><small class="text-muted">${receipt.customer_phone}</small>` : ''}
                    </div>
                    <div class="col-md-1">
                        <select class="form-select form-select-sm payment-method-select"
                                data-receipt-id="${receipt.id}"
                                onchange="updatePaymentMethod(${receipt.id}, this.value)">
                            <option value="Cash" ${receipt.payment_method === 'Cash' ? 'selected' : ''}>Cash</option>
                            <option value="Venmo" ${receipt.payment_method === 'Venmo' ? 'selected' : ''}>Venmo</option>
                            <option value="Zelle" ${receipt.payment_method === 'Zelle' ? 'selected' : ''}>Zelle</option>
                            <option value="Square" ${receipt.payment_method === 'Square' ? 'selected' : ''}>Square</option>
                            <option value="Stripe" ${receipt.payment_method === 'Stripe' ? 'selected' : ''}>Stripe</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <div class="fw-bold">$${parseFloat(receipt.total_amount).toFixed(2)}</div>
                    </div>
                    <div class="col-md-2">
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-outline-primary"
                                    onclick="viewReceiptDetails(${receipt.id})"
                                    title="View Details">
                                <span class="icon-symbol">👁</span>
                            </button>
                            <button class="btn btn-sm btn-outline-danger"
                                    onclick="deleteReceipt(${receipt.id})"
                                    title="Delete Receipt">
                                <span class="icon-symbol">🗑</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = batchControls + receiptItems;
        console.log('Receipt history displayed successfully');
    }

    /**
     * View Receipt Details
     */
    async viewReceiptDetails(receiptId) {
        try {
            const response = await fetch(`php/get_receipt.php?id=${receiptId}`);
            const data = await response.json();

            if (data.success) {
                this.showReceiptDetailsModal(data.data);
            } else {
                this.showMessage('Failed to load receipt details', 'error');
            }
        } catch (error) {
            console.error('Error loading receipt details:', error);
            this.showMessage('Error occurred while loading receipt details', 'error');
        }
    }

    /**
     * Show Receipt Details Modal
     */
    showReceiptDetailsModal(receipt) {
        // Create modal HTML with professional design - English interface
        const modalHtml = `
            <div class="modal fade" id="receiptDetailsModal" tabindex="-1" aria-labelledby="receiptDetailsModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-xxl receipt-details-modal">
                    <div class="modal-content receipt-details-content">
                        <div class="modal-header receipt-details-header">
                            <div class="receipt-header-info">
                                <h4 class="modal-title receipt-title" id="receiptDetailsModalLabel">
                                    <span class="receipt-icon">📄</span>
                                    <span class="receipt-title-text">Receipt Details</span>
                                </h4>
                                <div class="receipt-number-badge">
                                    <span class="receipt-number-label">Receipt Number</span>
                                    <span class="receipt-number-value">${receipt.receipt_number}</span>
                                </div>
                            </div>
                            <button type="button" class="btn-close receipt-close-btn" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body receipt-details-body">
                            <div class="receipt-content-grid">
                                <!-- Customer Information Card -->
                                <div class="receipt-info-card customer-info-card">
                                    <div class="card-header-modern">
                                        <div class="card-icon customer-icon">👤</div>
                                        <h5 class="card-title-modern">Customer Information</h5>
                                    </div>
                                    <div class="card-body-modern">
                                        <div class="info-row">
                                            <span class="info-label">Name</span>
                                            <span class="info-value">${receipt.customer_name || 'Not provided'}</span>
                                        </div>
                                        <div class="info-row">
                                            <span class="info-label">Phone</span>
                                            <span class="info-value">${receipt.customer_phone || 'Not provided'}</span>
                                        </div>
                                        <div class="info-row">
                                            <span class="info-label">Email</span>
                                            <span class="info-value">${receipt.customer_email || 'Not provided'}</span>
                                        </div>
                                        <div class="info-row">
                                            <span class="info-label">Address</span>
                                            <span class="info-value">${receipt.customer_address || 'Not provided'}</span>
                                        </div>
                                        ${receipt.notes ? `
                                        <div class="info-row">
                                            <span class="info-label">Notes</span>
                                            <span class="info-value">${receipt.notes}</span>
                                        </div>
                                        ` : ''}
                                    </div>
                                </div>

                                <!-- Receipt Information Card -->
                                <div class="receipt-info-card receipt-summary-card">
                                    <div class="card-header-modern">
                                        <div class="card-icon receipt-icon">🧾</div>
                                        <h5 class="card-title-modern">Receipt Information</h5>
                                    </div>
                                    <div class="card-body-modern">
                                        <div class="info-row">
                                            <span class="info-label">Created Date</span>
                                            <span class="info-value">${new Date(receipt.created_at).toLocaleDateString('en-US', {
                                                year: 'numeric',
                                                month: 'long',
                                                day: 'numeric',
                                                hour: '2-digit',
                                                minute: '2-digit'
                                            })}</span>
                                        </div>
                                        <div class="info-row">
                                            <span class="info-label">Payment Method</span>
                                            <span class="payment-method-badge">${receipt.payment_method || 'Cash'}</span>
                                        </div>
                                        <div class="financial-summary">
                                            <div class="financial-row">
                                                <span class="financial-label">Subtotal</span>
                                                <span class="financial-value">$${parseFloat(receipt.subtotal || 0).toFixed(2)}</span>
                                            </div>
                                            ${receipt.discount_amount > 0 ? `
                                            <div class="financial-row discount-row">
                                                <span class="financial-label">Discount</span>
                                                <span class="financial-value discount-value">-$${parseFloat(receipt.discount_amount).toFixed(2)}</span>
                                            </div>
                                            ` : ''}
                                            ${receipt.tax_amount > 0 ? `
                                            <div class="financial-row">
                                                <span class="financial-label">Tax</span>
                                                <span class="financial-value">$${parseFloat(receipt.tax_amount).toFixed(2)}</span>
                                            </div>
                                            ` : ''}
                                            <div class="financial-row total-row">
                                                <span class="financial-label total-label">Total</span>
                                                <span class="financial-value total-value">$${parseFloat(receipt.total_amount).toFixed(2)}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Items Section -->
                            <div class="receipt-info-card items-card">
                                <div class="card-header-modern">
                                    <div class="card-icon items-icon">🖥️</div>
                                    <h5 class="card-title-modern">Item Details (${receipt.items ? receipt.items.length : 0} items)</h5>
                                </div>
                                <div class="card-body-modern">
                                    ${receipt.items && receipt.items.length > 0 ? `
                                        <div class="items-table-container">
                                            <table class="items-table">
                                                <thead>
                                                    <tr>
                                                        <th>Item Name</th>
                                                        <th>Category</th>
                                                        <th>Description</th>
                                                        <th class="text-center">Qty</th>
                                                        <th class="text-end">Original Price</th>
                                                        <th class="text-end">Special Price</th>
                                                        <th class="text-center">Discount %</th>
                                                        <th class="text-end">Line Total</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    ${receipt.items.map(item => `
                                                        <tr class="item-row">
                                                            <td class="item-name">${item.item_name || 'N/A'}</td>
                                                            <td class="item-category">
                                                                ${item.category ? `<span class="category-badge">${item.category}</span>` : '<span class="no-category">Uncategorized</span>'}
                                                            </td>
                                                            <td class="item-description">${item.description || item.item_description || 'No description'}</td>
                                                            <td class="item-quantity text-center">${item.quantity || 1}</td>
                                                            <td class="item-original-price text-end">$${parseFloat(item.original_price || item.unit_price || 0).toFixed(2)}</td>
                                                            <td class="item-special-price text-end">$${parseFloat(item.special_price || item.unit_price || 0).toFixed(2)}</td>
                                                            <td class="item-discount text-center">${parseInt(item.discount_percent || 0)}%</td>
                                                            <td class="item-total text-end">$${parseFloat(item.total_price || item.line_total || 0).toFixed(2)}</td>
                                                        </tr>
                                                    `).join('')}
                                                </tbody>
                                            </table>
                                        </div>
                                    ` : `
                                        <div class="empty-items-state">
                                            <div class="empty-icon">📦</div>
                                            <p class="empty-text">No items found in this receipt</p>
                                        </div>
                                    `}
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer receipt-details-footer">
                            <button type="button" class="btn-modern btn-edit" onclick="editReceiptDetails(${receipt.id})">
                                <span class="btn-icon">✏️</span>
                                <span class="btn-text">Edit Receipt</span>
                            </button>
                            <button type="button" class="btn-modern btn-close-modal" data-bs-dismiss="modal">
                                <span class="btn-icon">❌</span>
                                <span class="btn-text">Close</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if present
        const existingModal = document.getElementById('receiptDetailsModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = Modal.getInstance(document.getElementById('receiptDetailsModal'));
        modal.show();

        // Clean up modal when hidden (using custom event)
        const modalElement = document.getElementById('receiptDetailsModal');
        modalElement.addEventListener('click', function(e) {
            if (e.target === modalElement) {
                modal.hide();
                setTimeout(() => this.remove(), 300);
            }
        });
    }

    /**
     * Edit Receipt Details
     */
    async editReceiptDetails(receiptId) {
        try {
            const response = await fetch(`php/get_receipt.php?id=${receiptId}`);
            const data = await response.json();

            if (data.success) {
                const receipt = data.data;

                // Close the details modal
                const detailsModal = Modal.getInstance(document.getElementById('receiptDetailsModal'));
                if (detailsModal) {
                    detailsModal.hide();
                }

                // Switch to create section
                this.showSection('create');

                // Populate form with receipt data
                this.populateFormWithReceiptData(receipt);

                this.showMessage('Receipt loaded for editing', 'info');
            } else {
                this.showMessage('Failed to load receipt for editing', 'error');
            }
        } catch (error) {
            console.error('Error loading receipt for editing:', error);
            this.showMessage('Error occurred while loading receipt for editing', 'error');
        }
    }

    /**
     * Populate Form with Receipt Data
     */
    populateFormWithReceiptData(receipt) {
        // Populate customer information
        const customerName = document.getElementById('customerName');
        const customerPhone = document.getElementById('customerPhone');
        const customerEmail = document.getElementById('customerEmail');
        const customerAddress = document.getElementById('customerAddress');
        const notes = document.getElementById('notes');
        const receiptNumber = document.getElementById('receiptNumber');

        if (customerName) customerName.value = receipt.customer_name || '';
        if (customerPhone) customerPhone.value = receipt.customer_phone || '';
        if (customerEmail) customerEmail.value = receipt.customer_email || '';
        if (customerAddress) customerAddress.value = receipt.customer_address || '';
        if (notes) notes.value = receipt.notes || '';
        if (receiptNumber) receiptNumber.value = receipt.receipt_number || '';

        // Populate items if ItemManager is available
        if (window.ItemManager && receipt.items) {
            // Clear existing items
            window.ItemManager.setReceiptItems([]);

            // Add receipt items with correct price structure
            receipt.items.forEach(item => {
                const receiptItem = {
                    id: Date.now() + Math.random(), // Generate temporary ID
                    name: item.item_name,
                    description: item.item_description || '',
                    category: item.category || '',
                    quantity: parseInt(item.quantity) || 1,
                    unitPrice: parseFloat(item.unit_price) || 0,
                    totalPrice: parseFloat(item.total_price) || 0,
                    originalPrice: parseFloat(item.original_price || item.unit_price) || 0,
                    specialPrice: parseFloat(item.special_price || item.unit_price) || 0,
                    discountPercent: parseInt(item.discount_percent || 0) || 0,
                    hidePrice: Boolean(item.hide_price || false)
                };

                // Directly add to receiptItems array to avoid re-processing
                window.ItemManager.receiptItems.push(receiptItem);
            });

            // Update display after all items are added
            window.ItemManager.updateReceiptItemsDisplay();
            window.ItemManager.updateTotals();
        }

        // Set discount and tax if available
        const discountAmount = document.getElementById('discountAmount');
        const taxRate = document.getElementById('taxRate');

        if (discountAmount && receipt.discount_amount) {
            discountAmount.value = parseFloat(receipt.discount_amount);
        }

        if (taxRate && receipt.tax_amount && receipt.subtotal) {
            const taxRatePercent = (parseFloat(receipt.tax_amount) / parseFloat(receipt.subtotal)) * 100;
            taxRate.value = taxRatePercent.toFixed(1);
        }
    }

    /**
     * Print Receipt Details
     */
    printReceiptDetails(receiptId) {
        // Get the modal content
        const modalContent = document.querySelector('#receiptDetailsModal .modal-body');
        if (!modalContent) {
            this.showMessage('Unable to print receipt', 'error');
            return;
        }

        // Create a new window for printing
        const printWindow = window.open('', '_blank');

        const printContent = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Receipt ${receiptId}</title>
                <link href="css/bootstrap.min.css" rel="stylesheet">
                <style>
                    body { font-family: Arial, sans-serif; }
                    .card { border: 1px solid #ddd; margin-bottom: 20px; }
                    .card-header { background-color: #f8f9fa; padding: 10px; font-weight: bold; }
                    .card-body { padding: 15px; }
                    .badge { padding: 4px 8px; border-radius: 4px; }
                    .bg-primary { background-color: #0d6efd !important; color: white; }
                    .bg-secondary { background-color: #6c757d !important; color: white; }
                    .text-primary { color: #0d6efd !important; }
                    .text-success { color: #198754 !important; }
                    .table { width: 100%; border-collapse: collapse; }
                    .table th, .table td { padding: 8px; border: 1px solid #ddd; }
                    .table-striped tbody tr:nth-of-type(odd) { background-color: #f9f9f9; }
                    .text-center { text-align: center; }
                    .text-end { text-align: right; }
                    @media print {
                        .no-print { display: none; }
                    }
                </style>
            </head>
            <body>
                ${modalContent.innerHTML}
            </body>
            </html>
        `;

        printWindow.document.write(printContent);
        printWindow.document.close();

        // Wait for content to load then print
        printWindow.onload = function () {
            printWindow.print();
            printWindow.close();
        };
    }

    /**
     * Search Receipts
     */
    async searchReceipts() {
        const searchTerm = document.getElementById('searchInput')?.value.trim() || '';
        const paymentMethod = document.getElementById('paymentFilter')?.value || '';
        const dateFrom = document.getElementById('dateFromFilter')?.value || '';
        const dateTo = document.getElementById('dateToFilter')?.value || '';

        const params = new URLSearchParams();
        if (searchTerm) params.append('search', searchTerm);
        if (paymentMethod) params.append('payment_method', paymentMethod);
        if (dateFrom) params.append('date_from', dateFrom);
        if (dateTo) params.append('date_to', dateTo);

        try {
            const response = await fetch(`php/get_receipts.php?${params.toString()}`);
            const data = await response.json();

            if (data.success) {
                this.displayReceiptHistory(data.data.receipts);
            } else {
                this.showMessage('Search failed', 'error');
            }
        } catch (error) {
            console.error('Error searching receipts:', error);
            this.showMessage('Error occurred during search', 'error');
        }
    }

    /**
     * Clear Filters
     */
    clearFilters() {
        const searchInput = document.getElementById('searchInput');
        const paymentFilter = document.getElementById('paymentFilter');
        const dateFromFilter = document.getElementById('dateFromFilter');
        const dateToFilter = document.getElementById('dateToFilter');

        if (searchInput) searchInput.value = '';
        if (paymentFilter) paymentFilter.value = '';
        if (dateFromFilter) dateFromFilter.value = '';
        if (dateToFilter) dateToFilter.value = '';

        this.loadReceiptHistory();
    }

    /**
     * Handle Search Key Press
     */
    handleSearchKeyPress(event) {
        if (event.key === 'Enter') {
            this.searchReceipts();
        }
    }

    /**
     * Show Preset Modal
     */
    showPresetModal() {
        // Wait a bit for presetManager to load if it's not ready yet
        if (typeof window.presetManager === 'undefined') {
            setTimeout(() => {
                this.showPresetModal();
            }, 100);
            return;
        }

        // Check if presetManager exists and has the method
        if (window.presetManager && typeof window.presetManager.showPresetModal === 'function') {
            window.presetManager.showPresetModal();
        } else {
            // Fallback implementation using pure CSS modal
            const modal = document.getElementById('presetModal');
            if (modal) {
                modal.classList.add('is-open');
                modal.setAttribute('aria-hidden', 'false');
            } else {
                const message = window.LanguageManager ?
                    window.LanguageManager.getText('preset_feature_unavailable') :
                    'Preset items feature is temporarily unavailable';
                this.showMessage(message, 'warning');
            }
        }
    }

    /**
     * Show Configuration Modal
     */
    showConfigurationModal() {
        // Check if configManager exists
        if (typeof window.configManager !== 'undefined' && window.configManager.showConfigurationModal) {
            window.configManager.showConfigurationModal();
        } else {
            this.showMessage('Configuration management feature is temporarily unavailable', 'warning');
        }
    }

    /**
     * Save Receipt Configuration
     */
    saveReceiptConfiguration() {
        // Check if configManager exists
        if (typeof window.configManager !== 'undefined' && window.configManager.saveReceiptConfiguration) {
            window.configManager.saveReceiptConfiguration();
        } else {
            this.showMessage('Configuration save feature is temporarily unavailable', 'warning');
        }
    }

    /**
     * Toggle Compact View
     */
    toggleCompactView() {
        if (window.ItemManager) {
            window.ItemManager.toggleCompactView();
            this.showMessage('View mode toggled', 'info');
        }
    }

    /**
     * Toggle Drag and Drop
     */
    toggleDragAndDrop() {
        if (window.ItemManager) {
            window.ItemManager.toggleDragAndDrop();
            const isEnabled = window.ItemManager.isDragDropEnabled;
            this.showMessage(`Drag and drop ${isEnabled ? 'enabled' : 'disabled'}`, 'info');
        }
    }

    /**
     * Get current section
     */
    getCurrentSection() {
        return this.currentSection;
    }

    /**
     * Initialize UI components
     */
    initializeUI() {
        // Set up any additional UI components
        this.setupTooltips();
        this.setupKeyboardShortcuts();
    }

    /**
     * Setup Tooltips
     */
    setupTooltips() {
        // Tooltips functionality removed - using title attributes instead
        // Custom tooltip implementation can be added here if needed
        console.log('Tooltips setup completed (using native title attributes)');
    }

    /**
     * Setup Keyboard Shortcuts
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+S to save receipt
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                if (window.ReceiptGenerator) {
                    window.ReceiptGenerator.saveReceipt();
                }
            }

            // Ctrl+P to print receipt
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                if (window.ReceiptGenerator) {
                    window.ReceiptGenerator.printReceipt();
                }
            }

            // Ctrl+N to clear form
            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                this.clearForm();
            }
        });
    }
}

// Create global instance
window.UIManager = new UIManager();

// Initialize UI when DOM is ready
document.addEventListener('DOMContentLoaded', function () {
    console.log('UIManager initializing...');
    window.UIManager.initializeUI();
});

// Export functions for backward compatibility
window.showMessage = (message, type, duration) => window.UIManager.showMessage(message, type, duration);
window.showSection = (sectionName) => window.UIManager.showSection(sectionName);
window.clearForm = () => window.UIManager.clearForm();
window.viewReceiptDetails = (receiptId) => window.UIManager.viewReceiptDetails(receiptId);
window.editReceiptDetails = (receiptId) => window.UIManager.editReceiptDetails(receiptId);
window.printReceiptDetails = (receiptId) => window.UIManager.printReceiptDetails(receiptId);
window.searchReceipts = () => window.UIManager.searchReceipts();
window.clearFilters = () => window.UIManager.clearFilters();
window.handleSearchKeyPress = (event) => window.UIManager.handleSearchKeyPress(event);
window.showPresetModal = () => window.UIManager.showPresetModal();
window.showConfigurationModal = () => window.UIManager.showConfigurationModal();
window.saveReceiptConfiguration = () => window.UIManager.saveReceiptConfiguration();

/**
 * 更新收據的付款方式
 */
async function updatePaymentMethod(receiptId, newPaymentMethod) {
    console.log('Updating payment method:', receiptId, newPaymentMethod);

    try {
        const response = await fetch('php/update_payment_method.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                receipt_id: receiptId,
                payment_method: newPaymentMethod
            })
        });

        console.log('Response status:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Response data:', data);

        if (data.success) {
            // Show success message
            if (window.UIManager) {
                window.UIManager.showMessage('Payment method updated successfully', 'success');
            }
        } else {
            // Show error message and revert the select
            if (window.UIManager) {
                window.UIManager.showMessage('Failed to update payment method: ' + data.message, 'error');
            }
            // Reload to revert the change
            window.UIManager.loadReceiptHistory();
        }
    } catch (error) {
        console.error('Error updating payment method:', error);
        if (window.UIManager) {
            window.UIManager.showMessage('Error updating payment method: ' + error.message, 'error');
        }
        // Reload to revert the change
        window.UIManager.loadReceiptHistory();
    }
}

// Export updatePaymentMethod function
window.updatePaymentMethod = updatePaymentMethod;

console.log('UIManager loaded successfully');