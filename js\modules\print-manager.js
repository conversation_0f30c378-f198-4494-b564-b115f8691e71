/**
 * Print Manager Module
 * Handles printing functionality for receipts
 */
class PrintManager {
    constructor() {
        this.defaultPaymentMethod = 'Cash';
    }

    /**
     * Generate Receipt HTML for Print with Pagination
     * Implements proper pagination with max 20 items per page
     */
    generateReceiptHtmlForPrint(data) {
        // Implement proper pagination logic
        return this.generatePaginatedReceiptHtml(data);
    }

    /**
     * Generate Paginated Receipt HTML
     * Max 20 items per page, Payment Method and Signature always on last page bottom
     */
    generatePaginatedReceiptHtml(data) {
        const formatCurrency = (amount) => {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        };

        // Two-logic pagination system:
        // Logic 1: Page generation triggers (when to create new pages)
        // Logic 2: Item distribution (how to fill pages with items)
        const items = data.items || [];
        const totalItems = items.length;
        const pages = [];

        if (totalItems <= 3) {
            // 3 or fewer items: everything on first page (items + Payment Method + Signature)
            pages.push(items);
        } else {
            // Logic 1: Determine how many pages to generate based on triggers
            let totalPagesNeeded = 1; // Always have at least first page

            if (totalItems > 3) totalPagesNeeded = 2;   // Generate 2nd page when items > 3
            if (totalItems > 30) totalPagesNeeded = 3;  // Generate 3rd page when items > 30
            if (totalItems > 50) totalPagesNeeded = 4;  // Generate 4th page when items > 50
            if (totalItems > 70) totalPagesNeeded = 5;  // Generate 5th page when items > 70
            // Continue pattern: every +20 items after 50 triggers new page
            if (totalItems > 70) {
                totalPagesNeeded = 4 + Math.ceil((totalItems - 70) / 20);
            }

            // Logic 2: Distribute items across pages (20 items per page)
            for (let pageIndex = 0; pageIndex < totalPagesNeeded; pageIndex++) {
                const startIndex = pageIndex * 20;
                const endIndex = Math.min(startIndex + 20, totalItems);
                const pageItems = items.slice(startIndex, endIndex);
                pages.push(pageItems);
            }
        }

        // If no items, create at least one page
        if (pages.length === 0) {
            pages.push([]);
        }

        // Set needsSummaryPage flag for Payment Method placement logic
        const needsSummaryPage = totalItems > 3;

        // Generate HTML for all pages
        let allPagesHtml = '';

        pages.forEach((pageItems, pageIndex) => {
            const isFirstPage = pageIndex === 0;
            const isLastPage = pageIndex === pages.length - 1;
            const isSummaryPage = needsSummaryPage && isLastPage && pageItems.length === 0;

            // Determine if Payment Method & Signature should show on this page
            const showPaymentAndSignature = (totalItems <= 3 && isFirstPage) ||
                                          (totalItems > 3 && isLastPage);

            allPagesHtml += this.generateSinglePageHtml(data, pageItems, pageIndex + 1, pages.length, isFirstPage, isLastPage, isSummaryPage, showPaymentAndSignature, totalItems, formatCurrency);
        });

        return allPagesHtml;
    }

    /**
     * Generate HTML for a single page
     */
    generateSinglePageHtml(data, pageItems, pageNumber, totalPages, isFirstPage, isLastPage, isSummaryPage, showPaymentAndSignature, totalItems, formatCurrency) {
        return `
            <div class="receipt-container">
                <div class="receipt-inner">
                    ${window.currentLogo && window.currentLogo.src ? `
                    <div class="receipt-logo">
                        <img src="${window.currentLogo.src}" alt="Company Logo">
                    </div>
                    ` : ''}

                    <div class="preview-receipt-header">
                        <div class="receipt-title">${window.LanguageManager ? window.LanguageManager.getText('company_name') : 'KelvinKMS'}</div>
                        <div class="receipt-company-info">
                            ${window.LanguageManager ? window.LanguageManager.getText('company_website') : 'KelvinKMS.com'}<br>
                            ${window.LanguageManager ? window.LanguageManager.getText('company_phone') : '************'}<br>
                            ${window.LanguageManager ? window.LanguageManager.getText('company_email') : '<EMAIL>'}
                        </div>
                    </div>

                    <div class="preview-receipt-info">
                        <div>
                            <div class="receipt-number">
                                Receipt Number: ${data.receiptNumber}
                            </div>
                            ${totalPages > 1 ? `<div class="page-info">Page ${pageNumber} of ${totalPages}</div>` : ''}
                        </div>
                    </div>

                    ${isFirstPage ? `
                    <div class="preview-customer-info">
                        <h6>Customer Information</h6>
                        <div class="customer-field">
                            <span class="customer-field-label">Name:</span>
                            <span class="customer-field-value">${data.customer.name || ''}</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Phone:</span>
                            <span class="customer-field-value">${data.customer.phone || ''}</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Email:</span>
                            <span class="customer-field-value">${data.customer.email || ''}</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Address:</span>
                            <span class="customer-field-value">${data.customer.address || ''}</span>
                        </div>
                    </div>
                    ` : ''}

                    ${!isSummaryPage ? `
                    <div class="receipt-items">
                        <div class="items-header">
                            <h6>Items ${totalPages > 1 ? `(Page ${pageNumber} of ${totalPages})` : `(Total: ${data.items.length} items)`}</h6>
                        </div>
                        <table class="receipt-table">
                            <thead>
                                <tr>
                                    <th class="text-center receipt-table-header-number">#</th>
                                    <th class="text-left">Item</th>
                                    <th class="text-right receipt-table-header-price">Orig. Price</th>
                                    <th class="text-right receipt-table-header-discount">Disc.</th>
                                    <th class="text-right receipt-table-header-price">Final Price</th>
                                    <th class="text-center receipt-table-header-qty">Qty</th>
                                    <th class="text-right receipt-table-header-total">Total Price</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${pageItems.map((item, pageItemIndex) => {
                                    // Simple calculation: 20 items per page starting from page 1
                                    const globalIndex = (pageNumber - 1) * 20 + pageItemIndex;
                                    const originalPrice = item.originalPrice || item.unitPrice;
                                    const specialPrice = item.specialPrice || item.unitPrice;
                                    const discountPercent = item.discountPercent || 0;
                                    const actualFinalPrice = item.hidePrice ? 0 : specialPrice;

                                    return `
                                        <tr class="receipt-table-row-compact">
                                            <td class="text-center item-number receipt-table-cell-compact receipt-table-cell-number">${globalIndex + 1}</td>
                                            <td class="text-left receipt-table-cell-compact">
                                                <div class="item-name receipt-table-item-name">${item.name}</div>
                                                ${item.description ? `<div class="item-description receipt-table-item-desc">${item.description}</div>` : ''}
                                            </td>
                                            <td class="text-right receipt-table-cell-compact receipt-table-cell-price">${item.hidePrice ? 'N/A' : formatCurrency(originalPrice)}</td>
                                            <td class="text-right receipt-table-cell-compact receipt-table-cell-price">${item.hidePrice ? 'N/A' : (discountPercent > 0 ? discountPercent + '%' : '-')}</td>
                                            <td class="text-right receipt-table-cell-compact receipt-table-cell-price">${item.hidePrice ? 'N/A' : formatCurrency(actualFinalPrice)}</td>
                                            <td class="text-center receipt-table-cell-compact receipt-table-cell-qty">${item.quantity}</td>
                                            <td class="text-right receipt-table-cell-compact receipt-table-cell-total">${item.hidePrice ? 'N/A' : formatCurrency(item.totalPrice)}</td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>
                    ` : ''}

                    ${showPaymentAndSignature ? `
                    <div class="receipt-bottom-fixed">
                        <div class="receipt-totals">
                            <table class="totals-table">
                                <tr>
                                    <td class="label">Subtotal:</td>
                                    <td class="amount">${formatCurrency(data.totals.subtotal)}</td>
                                </tr>
                                ${data.totals.discount > 0 ? `
                                <tr>
                                    <td class="label">Discount:</td>
                                    <td class="amount">-${formatCurrency(data.totals.discount)}</td>
                                </tr>
                                ` : ''}
                                ${data.totals.tax > 0 ? `
                                <tr>
                                    <td class="label">Tax:</td>
                                    <td class="amount">${formatCurrency(data.totals.tax)}</td>
                                </tr>
                                ` : ''}
                                <tr class="total-row">
                                    <td class="label">Total:</td>
                                    <td class="amount">${formatCurrency(data.totals.total)}</td>
                                </tr>
                            </table>
                        </div>

                        <div class="payment-method">
                            <div class="payment-method-title">
                                Payment Method
                            </div>
                            <div class="payment-options">
                                ${['Cash', 'Venmo', 'Zelle', 'Square', 'Stripe'].map(pm => {
                                    return `
                                        <div class="payment-option-button">
                                            <div class="payment-checkbox"></div>
                                            <span class="payment-label">${pm}</span>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        </div>

                        ${data.notes ? `
                        <div class="receipt-notes">
                            <h6>Notes</h6>
                            <p>${data.notes}</p>
                        </div>
                        ` : ''}

                        <div class="signature-section">
                            <div class="signature-labels-row">
                                <div class="signature-label-item">
                                    <span class="signature-label">Seller Signature:</span>
                                </div>
                                <div class="signature-label-item">
                                    <span class="signature-label">Buyer Signature:</span>
                                </div>
                                <div class="signature-label-item">
                                    <span class="signature-label">Signature Date:</span>
                                </div>
                            </div>
                            <div class="signature-lines-area">
                                <div class="signature-line-space"></div>
                            </div>
                        </div>
                    </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * Print Receipt
     */
    printReceipt() {
        // Get fresh receipt data instead of using cached HTML
        const receiptItems = window.ItemManager ? window.ItemManager.getReceiptItems() : [];
        if (receiptItems.length === 0) {
            if (typeof UIManager !== 'undefined') {
                const message = window.LanguageManager ?
                    window.LanguageManager.getText('error_no_items_to_print') :
                    'Please add items to the receipt first';
                UIManager.showMessage(message, 'warning');
            }
            return;
        }

        // Generate fresh receipt data for printing
        const customerName = document.getElementById('customerName')?.value.trim() || '';
        const customerPhone = document.getElementById('customerPhone')?.value.trim() || '';
        const customerEmail = document.getElementById('customerEmail')?.value.trim() || '';

        const receiptData = {
            customer: {
                name: customerName,
                phone: customerPhone,
                email: customerEmail,
                address: document.getElementById('customerAddress')?.value.trim() || ''
            },
            paymentMethod: this.defaultPaymentMethod,
            items: receiptItems.map(item => ({
                name: item.name,
                category: item.category,
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice,
                originalPrice: item.originalPrice,
                specialPrice: item.specialPrice,
                discountPercent: item.discountPercent,
                hidePrice: item.hidePrice
            })),
            totals: window.ItemManager ? window.ItemManager.calculateTotals() : this.calculateTotals(receiptItems),
            notes: document.getElementById('notes')?.value.trim() || '',
            receiptNumber: document.getElementById('receiptNumber')?.value || 'KMS-UltraVIP-0000001'
        };

        // Generate print HTML
        const receiptContent = this.generateReceiptHtmlForPrint(receiptData);

        // Open print window
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        if (!printWindow) {
            alert('Please allow popups for this site to enable printing.');
            return;
        }

        const printHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Receipt - ${receiptData.receiptNumber}</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                <link href="css/receipt.css" rel="stylesheet">
                <link href="css/receipt-preview.css" rel="stylesheet">
                <link href="css/style.css" rel="stylesheet">
                <style>
                    @page {
                        size: letter;
                        margin: 0;
                    }

                    body {
                        margin: 0;
                        padding: 0;
                        font-family: 'Courier New', monospace;
                        background: white;
                        -webkit-print-color-adjust: exact !important;
                        print-color-adjust: exact !important;
                    }

                    .receipt-container { height: 11in; display: flex; flex-direction: column; box-sizing: border-box; }
                    .receipt-inner { flex: 1 1 auto; display: flex; flex-direction: column; }
                    .receipt-bottom-fixed { margin-top: auto; }

                    .receipt-page {
                        page-break-after: always;
                        margin: 0;
                        padding: 0;
                    }

                    .receipt-page:last-child {
                        page-break-after: auto;
                    }

                    /* Use the same styles as Receipt Preview */
                    .receipt-container {
                        position: relative;
                        max-width: 100%;
                        margin: 0 auto;
                        padding: 2rem 2.5rem;
                        font-family: 'Courier New', monospace;
                        font-size: 12pt;
                        line-height: 1.4;
                        background: white;
                        border-radius: 60px;
                        box-shadow:
                            0 0 0 3px #D4AF37,
                            0 0 0 6px white,
                            0 0 0 9px #B8860B,
                            0 0 0 12px white,
                            0 0 0 15px #DAA520,
                            0 8px 30px rgba(0, 0, 0, 0.2),
                            inset 0 0 0 2px #F5DEB3;
                        background-image:
                            radial-gradient(circle at 20px 20px, #F5DEB3 2px, transparent 2px),
                            radial-gradient(circle at 60px 60px, #F5DEB3 1px, transparent 1px);
                        background-size: 80px 80px, 40px 40px;
                        background-position: 0 0, 20px 20px;
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                    }

                    .no-print {
                        display: none !important;
                    }
                </style>
            </head>
            <body>
                ${receiptContent}
                <script>
                    window.onload = function() {
                        setTimeout(function() {
                            window.print();
                            window.close();
                        }, 1500);
                    };
                </script>
            </body>
            </html>
        `;

        printWindow.document.write(printHtml);
        printWindow.document.close();
    }

    /**
     * Calculate totals for items
     */
    calculateTotals(items) {
        let subtotal = 0;
        items.forEach(item => {
            subtotal += item.totalPrice || 0;
        });

        const discountAmount = parseFloat(document.getElementById('discountAmount')?.value) || 0;
        const taxRate = 0.1; // 10% tax rate
        const taxAmount = Math.max(0, (subtotal - discountAmount) * taxRate);
        const totalAmount = subtotal - discountAmount + taxAmount;

        return {
            subtotal: subtotal,
            discount: discountAmount,
            tax: taxAmount,
            total: Math.max(0, totalAmount)
        };
    }
}

// Create global instance
window.PrintManager = new PrintManager();

// Export for backward compatibility
window.printReceipt = () => window.PrintManager.printReceipt();
