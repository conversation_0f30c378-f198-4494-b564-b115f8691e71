/**
 * 預設項目管理模組
 * KMS PC Receipt Maker
 */

// 預設項目管理類
class PresetManager {
    constructor() {
        this.pcParts = [];
        this.draggedElement = null;
        this.draggedItemId = null;
    }

    /**
     * 載入電腦零件數據
     */
    async loadPcParts() {
        try {
            const response = await fetch('php/get_pc_parts.php');
            const data = await response.json();
            
            if (data.success) {
                // 後端以字串形式返回數值（DECIMAL/INT），在此統一轉為正確型別
                this.pcParts = (data.data || []).map(raw => {
                    const item = { ...raw };
                    // ID 轉為數字，避免嚴格等於比較失敗
                    item.id = raw.id !== undefined ? Number(raw.id) : raw.id;
                    // 價格欄位轉為數字或 null
                    const toNumOrNull = (v) => (v === null || v === undefined || v === '' ? null : Number(v));
                    item.default_price = toNumOrNull(raw.default_price);
                    item.original_price = toNumOrNull(raw.original_price);
                    item.special_price = toNumOrNull(raw.special_price);
                    // 排序欄位（若存在）轉為數字
                    if (raw.sort_order !== undefined) item.sort_order = Number(raw.sort_order);
                    return item;
                });
                return true;
            } else {
                console.error('Failed to load PC parts:', data.message);
                return false;
            }
        } catch (error) {
            console.error('Error loading PC parts:', error);
            return false;
        }
    }

    

    /**
     * 載入預設項目
     */
    loadPresetItems() {
        const searchTerm = (document.getElementById('presetSearch')?.value || '').trim().toLowerCase();
        const categoryFilter = document.getElementById('presetCategoryFilter')?.value || '';
        
        let filteredItems = this.pcParts;
        
        if (searchTerm) {
            filteredItems = filteredItems.filter(item => {
                const name = (item.name || '').toLowerCase();
                const desc = (item.description || '').toLowerCase();
                const cat = (item.category || '').toLowerCase();
                return name.includes(searchTerm) || desc.includes(searchTerm) || cat.includes(searchTerm);
            });
        }
        
        if (categoryFilter) {
            filteredItems = filteredItems.filter(item => this.categoryMatches(item.category, categoryFilter));
        }
        
        this.displayPresetItems(filteredItems);
    }

    /**
     * 顯示預設項目
     */
    displayPresetItems(items) {
        const container = document.getElementById('presetList');

        if (items.length === 0) {
            container.innerHTML = '<div class="preset-empty">沒有找到匹配的項目</div>';
            return;
        }

        let html = '';
        items.forEach((item, index) => {
            const hasSpecial = typeof item.special_price === 'number' && !isNaN(item.special_price) && item.special_price > 0;
            const hasOriginal = typeof item.original_price === 'number' && !isNaN(item.original_price) && item.original_price > 0;
            const priceToShow = hasSpecial ? item.special_price : (typeof item.default_price === 'number' ? item.default_price : 0);
            const discountPct = hasSpecial && hasOriginal && item.original_price > 0
                ? Math.round((1 - (item.special_price / item.original_price)) * 100)
                : null;
            html += `
                <div class="preset-item" 
                     draggable="true" 
                     data-item-id="${item.id}" 
                     data-index="${index}"
                     ondragstart="presetManager.handleDragStart(event)"
                     ondragover="presetManager.handleDragOver(event)"
                     ondrop="presetManager.handleDrop(event)"
                     ondragend="presetManager.handleDragEnd(event)">
                    <div class="preset-item-left">
                        <div class="drag-handle" title="拖拽排序">
                            <span class="icon-symbol">⋮⋮</span>
                        </div>
                    </div>
                    <div class="preset-item-main">
                        <div class="preset-title-line">
                            <strong class="preset-name">${item.name}</strong>
                            <span class="preset-category">[${item.category}]</span>
                            ${item.description ? `<span class="preset-desc">- ${item.description}</span>` : ''}
                        </div>
                    </div>
                    <div class="preset-item-actions">
                        <div class="preset-price-line">
                            ${hasSpecial && hasOriginal && item.original_price > item.special_price
                                ? `
                                   <span class="price-original">${LanguageManager.formatCurrency(item.original_price)}</span>
                                   <span class="price-special">${LanguageManager.formatCurrency(item.special_price)}</span>
                                   ${discountPct !== null ? `<span class=\"price-discount\">- ${discountPct}%</span>` : ''}
                                  `
                                : `<span class="price-default">${LanguageManager.formatCurrency(priceToShow)}</span>`}
                        </div>
                        <div class="preset-buttons-row">
                            <button type="button" class="btn-action btn-action-select" onclick="presetManager.selectPresetItem(${item.id})" title="Select Item">
                                <span class="icon-symbol">➕</span><span class="btn-text">Select</span>
                            </button>
                            <button type="button" class="btn-action btn-action-edit" onclick="presetManager.editPresetItem(${item.id})" title="Edit">
                                <span class="icon-symbol">✏</span><span class="btn-text">Edit</span>
                            </button>
                            <button type="button" class="btn-action btn-action-delete" onclick="presetManager.deletePresetItemWithoutConfirm(${item.id})" title="Delete">
                                <span class="icon-symbol">🗑</span><span class="btn-text">Delete</span>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    /**
     * 獲取分類文字
     */
    getCategoryText(category) {
        const categories = {
            // 與 UI 下拉選單一致
            'PC Case': 'PC機殼',
            'CPU': '處理器',
            'CPU Cooler': 'CPU 散熱器',
            'GPU': '顯示卡',
            'RAM': '記憶體',
            'SSD': 'SSD 固態硬碟',
            'Motherboard': '主機板',
            'PSU': '電源供應器',
            'MB RGB': '主機板 RGB',
            'GPU RGB': '顯示卡 RGB',
            'Fan RGB': '風扇 RGB',
            'Other': '其他',
            'Services': '服務',

            // 兼容舊資料庫可能使用的分類
            'Case': 'PC機殼',
            'Cooler': '散熱器',
            'Memory': '記憶體',
            'Service': '服務',
            'Storage': '儲存裝置',
            'Graphics Card': '顯示卡',
            'Power Supply': '電源供應器',
            'Cooling': '散熱系統',
            'Peripherals': '周邊設備',
            'Accessories': '配件'
        };
        return categories[category] || category;
    }

    /**
     * 分類匹配（支援同義詞與舊資料分類名稱）
     */
    categoryMatches(itemCategory, selected) {
        const normalize = (s) => (s || '').toString().trim().toLowerCase();
        const map = {
            'pc case': ['pc case', 'case'],
            'cpu': ['cpu'],
            'cpu cooler': ['cpu cooler', 'cooler', 'cooling'],
            'gpu': ['gpu', 'graphics card', 'vga'],
            'ram': ['ram', 'memory'],
            'ssd': ['ssd', 'storage'],
            'motherboard': ['motherboard', 'mb'],
            'psu': ['psu', 'power supply'],
            'mb rgb': ['mb rgb'],
            'gpu rgb': ['gpu rgb'],
            'fan rgb': ['fan rgb'],
            'other': ['other', 'accessories', 'peripherals'],
            'services': ['services', 'service']
        };
        const sel = normalize(selected);
        const aliases = map[sel] || [sel];
        const item = normalize(itemCategory);
        return aliases.includes(item);
    }

    /**
     * 選擇預設項目
     */
    selectPresetItem(itemId) {
        const item = this.pcParts.find(p => p.id === itemId);
        if (!item) return;

        // 使用新的模塊化系統中的addItemToReceipt函數
        let success = false;

        if (typeof window.addItemToReceipt === 'function') {
            window.addItemToReceipt(item);
            success = true;
        } else if (typeof window.ItemManager !== 'undefined' && window.ItemManager.addItemToReceipt) {
            // 直接使用 ItemManager
            window.ItemManager.addItemToReceipt(item);
            success = true;
        } else {
            console.error('addItemToReceipt function not found');
            console.log('Available functions:', {
                addItemToReceipt: typeof window.addItemToReceipt,
                ItemManager: typeof window.ItemManager,
                ItemManagerAddItem: window.ItemManager ? typeof window.ItemManager.addItemToReceipt : 'ItemManager not available'
            });
        }

        // 顯示消息
        if (success) {
            if (typeof window.showMessage === 'function') {
                window.showMessage('Item added successfully!', 'success');
            } else if (typeof window.UIManager !== 'undefined' && window.UIManager.showMessage) {
                window.UIManager.showMessage('Item added successfully!', 'success');
            } else {
                console.log('Item added successfully!');
            }
        } else {
            if (typeof window.showMessage === 'function') {
                window.showMessage('Failed to add item', 'error');
            } else if (typeof window.UIManager !== 'undefined' && window.UIManager.showMessage) {
                window.UIManager.showMessage('Failed to add item', 'error');
            } else {
                alert('Failed to add item');
            }
            return;
        }

        // 不關閉模態框，讓用戶可以繼續添加項目
        // const modal = bootstrap.Modal.getInstance(document.getElementById('presetModal'));
        // if (modal) {
        //     modal.hide();
        // }
    }

    /**
     * 顯示新增預設表單
     */
    showAddPresetForm() {
        const form = document.getElementById('addPresetForm');
        form.classList.remove('add-preset-form-hidden');
        document.getElementById('presetItemName').value = '';
        document.getElementById('presetItemCategory').value = 'PC Case';
        const priceEl = document.getElementById('presetItemPrice');
        if (priceEl) priceEl.value = '';
        const originalEl = document.getElementById('presetItemOriginalPrice');
        const specialEl = document.getElementById('presetItemSpecialPrice');
        const discountEl = document.getElementById('presetItemDiscountPercent');
        if (originalEl) originalEl.value = '';
        if (specialEl) specialEl.value = '';
        if (discountEl) discountEl.value = '';
        document.getElementById('presetItemDescription').value = '';

        // 綁定自動計算折扣
        this.bindDiscountAutoCalc();
    }

    /**
     * 取消新增預設
     */
    cancelAddPreset() {
        const form = document.getElementById('addPresetForm');
        form.classList.add('add-preset-form-hidden');
    }

    /**
     * 保存預設項目
     */
    async savePresetItem() {
        const name = document.getElementById('presetItemName').value.trim();
        const category = document.getElementById('presetItemCategory').value;
        const description = document.getElementById('presetItemDescription').value.trim();
        const originalVal = document.getElementById('presetItemOriginalPrice')?.value;
        const specialVal = document.getElementById('presetItemSpecialPrice')?.value;
        const legacyPriceVal = document.getElementById('presetItemPrice')?.value; // 兼容舊欄位

        // 基本驗證：名稱與分類必填
        if (!name) {
            alert(LanguageManager.getText('error_item_name_required') || '請輸入項目名稱');
            return;
        }
        if (!category) {
            alert(LanguageManager.getText('error_item_category_required') || '請選擇分類');
            return;
        }

        const payload = {
            id: window.currentEditingPresetId || null,
            name,
            category,
            description,
            original_price: originalVal !== undefined && originalVal !== '' ? parseFloat(originalVal) : null,
            special_price: specialVal !== undefined && specialVal !== '' ? parseFloat(specialVal) : null,
            default_price: legacyPriceVal !== undefined && legacyPriceVal !== '' ? parseFloat(legacyPriceVal) : null
        };

        try {
            const response = await fetch('php/save_pc_part.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });

            // 優先嘗試解析JSON，否則回退文本
            let data = null;
            try {
                data = await response.json();
            } catch (e) {
                const text = await response.text();
                console.error('save_pc_part non-JSON response:', text);
                showMessage('保存預設失敗：伺服器返回非JSON響應', 'error');
                return;
            }

            if (!response.ok || !data.success) {
                const msg = (data && data.message) ? data.message : `保存預設失敗 (HTTP ${response.status})`;
                showMessage(msg, 'error');
                return;
            }

            // 更新內存列表：重新載入資料庫
            await this.loadPcParts();

            this.cancelAddPreset();
            this.loadPresetItems();
            window.currentEditingPresetId = null;
            showMessage(LanguageManager.getText('preset_added_success') || '預設項目保存成功！', 'success');
        } catch (err) {
            console.error('Error saving preset item:', err);
            showMessage('保存預設時發生錯誤', 'error');
        }
    }

    /**
     * 編輯預設項目
     */
    editPresetItem(itemId) {
        const item = this.pcParts.find(p => p.id === itemId);
        if (!item) return;

        // 先顯示表單（避免之後填入的值被初始化流程清空）
        this.showAddPresetForm();

        // 設置編輯模式
        window.currentEditingPresetId = itemId;

        // 再填入欄位值
        document.getElementById('presetItemName').value = item.name;
        document.getElementById('presetItemCategory').value = item.category;
        const priceEl = document.getElementById('presetItemPrice');
        if (priceEl) priceEl.value = item.default_price ?? '';
        const originalEl = document.getElementById('presetItemOriginalPrice');
        const specialEl = document.getElementById('presetItemSpecialPrice');
        const discountEl = document.getElementById('presetItemDiscountPercent');
        if (originalEl) originalEl.value = item.original_price ?? '';
        if (specialEl) specialEl.value = item.special_price ?? '';
        if (discountEl) {
            if (item.original_price && item.special_price && item.original_price > 0) {
                discountEl.value = Math.round((1 - (item.special_price / item.original_price)) * 100);
            } else {
                discountEl.value = '';
            }
        }
        document.getElementById('presetItemDescription').value = item.description || '';

        // 確保折扣自動計算的事件綁定存在
        this.bindDiscountAutoCalc();
    }

    /**
     * 刪除預設項目（無確認框）
     */
    async deletePresetItemWithoutConfirm(itemId) {
        const itemIndex = this.pcParts.findIndex(p => p.id === itemId);
        if (itemIndex === -1) return;

        try {
            // 直接從資料庫刪除
            const response = await fetch('php/delete_pc_part.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ id: itemId })
            });
            const data = await response.json();
            if (!data.success) {
                showMessage(data.message || '刪除失敗', 'error');
                return;
            }

            // 從記憶體數組中移除
            this.pcParts.splice(itemIndex, 1);

            // 重新載入列表
            this.loadPresetItems();

            showMessage(LanguageManager.getText('preset_deleted_success') || '預設項目刪除成功！', 'success');
        } catch (error) {
            console.error('Error deleting preset item:', error);
            showMessage('刪除項目時發生錯誤', 'error');
        }
    }

    /**
     * 移動預設項目
     */
    movePresetItem(itemId, direction) {
        const currentIndex = this.pcParts.findIndex(p => p.id === itemId);
        if (currentIndex === -1) return;

        const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
        
        if (newIndex < 0 || newIndex >= this.pcParts.length) return;

        // 交換位置
        [this.pcParts[currentIndex], this.pcParts[newIndex]] = [this.pcParts[newIndex], this.pcParts[currentIndex]];

        // 更新資料庫中的排序
        this.saveOrderToDatabase();

        // 重新載入列表
        this.loadPresetItems();
    }

    // 拖拽相關方法
    handleDragStart(event) {
        this.draggedElement = event.target.closest('.preset-item');
        this.draggedItemId = parseInt(this.draggedElement.dataset.itemId);
        this.draggedElement.style.opacity = '0.5';
        event.dataTransfer.effectAllowed = 'move';
    }

    handleDragOver(event) {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'move';
        
        const targetElement = event.target.closest('.preset-item');
        if (targetElement && targetElement !== this.draggedElement) {
            targetElement.style.borderTop = '3px solid #667eea';
        }
    }

    handleDrop(event) {
        event.preventDefault();
        
        const targetElement = event.target.closest('.preset-item');
        if (targetElement && targetElement !== this.draggedElement) {
            const targetItemId = parseInt(targetElement.dataset.itemId);
            const draggedIndex = parseInt(this.draggedElement.dataset.index);
            const targetIndex = parseInt(targetElement.dataset.index);
            
            this.reorderPresetItems(this.draggedItemId, targetItemId, draggedIndex, targetIndex);
        }
        
        // 清除樣式
        document.querySelectorAll('.preset-item').forEach(item => {
            item.style.borderTop = '';
        });
    }

    handleDragEnd(event) {
        if (this.draggedElement) {
            this.draggedElement.style.opacity = '';
        }
        this.draggedElement = null;
        this.draggedItemId = null;
        
        // 清除所有拖拽樣式
        document.querySelectorAll('.preset-item').forEach(item => {
            item.style.borderTop = '';
        });
    }

    async reorderPresetItems(draggedId, targetId, draggedIndex, targetIndex) {
        const draggedItem = this.pcParts.find(item => item.id === draggedId);
        if (!draggedItem) return;

        // 從原位置移除
        this.pcParts.splice(draggedIndex, 1);

        // 插入到新位置
        const newTargetIndex = targetIndex > draggedIndex ? targetIndex - 1 : targetIndex;
        this.pcParts.splice(newTargetIndex, 0, draggedItem);

        // 保存順序到資料庫
        await this.saveOrderToDatabase();

        // 重新載入列表
        this.loadPresetItems();
    }

    async changePresetOrder(itemId, newPosition) {
        const newPos = parseInt(newPosition) - 1;

        if (newPos < 0 || newPos >= this.pcParts.length) {
            alert('位置編號超出範圍');
            this.loadPresetItems();
            return;
        }

        const currentIndex = this.pcParts.findIndex(item => item.id === itemId);
        if (currentIndex === -1) return;

        // 移動項目到新位置
        const item = this.pcParts.splice(currentIndex, 1)[0];
        this.pcParts.splice(newPos, 0, item);

        // 保存順序到資料庫
        await this.saveOrderToDatabase();

        // 重新載入列表
        this.loadPresetItems();
    }

    /**
     * 顯示預設項目模態框
     */
    async showPresetModal() {
        // 首先載入PC零件數據
        if (!this.pcParts || this.pcParts.length === 0) {
            await this.loadPcParts();
        }

        this.loadPresetItems();
        const modalEl = document.getElementById('presetModal');
        if (modalEl) {
            modalEl.classList.add('is-open');
            modalEl.setAttribute('aria-hidden', 'false');
            document.body.classList.add('kms-no-scroll');

            // 應用當前語言到模態框內容
            setTimeout(() => {
                // 直接更新語言文本
                const currentLang = window.LanguageManager ? window.LanguageManager.getCurrentLanguage() : 'zh';

                // 更新搜索輸入框佔位符
                const searchInput = document.getElementById('presetSearch');
                if (searchInput) {
                    if (currentLang === 'en') {
                        searchInput.placeholder = 'Search items...';
                    } else {
                        searchInput.placeholder = '搜索項目...';
                    }
                }

                // 更新分類篩選器的第一個選項
                const categoryFilter = document.getElementById('presetCategoryFilter');
                if (categoryFilter && categoryFilter.options.length > 0) {
                    if (currentLang === 'en') {
                        categoryFilter.options[0].textContent = 'All categories';
                    } else {
                        categoryFilter.options[0].textContent = '所有分類';
                    }
                }
            }, 100);

            // close on ESC
            const onKeyDown = (e) => {
                if (e.key === 'Escape') {
                    this.hidePresetModal();
                }
            };
            // click backdrop to close
            const onBackdropClick = (e) => {
                if (e.target === modalEl) {
                    this.hidePresetModal();
                }
            };
            // save handlers to element for later removal
            modalEl._kmsEscHandler = onKeyDown;
            modalEl._kmsBackdropHandler = onBackdropClick;
            document.addEventListener('keydown', onKeyDown);
            modalEl.addEventListener('click', onBackdropClick);
        }
    }

    /**
     * 更新模態框語言
     */
    updateModalLanguage() {
        if (!window.LanguageManager) return;

        const currentLang = window.LanguageManager.getCurrentLanguage();

        // 更新搜索輸入框佔位符
        const searchInput = document.getElementById('presetSearch');
        if (searchInput) {
            const searchText = window.LanguageManager.getText('search_items');
            if (searchText && searchText !== 'search_items') {
                searchInput.placeholder = searchText;
            }
        }

        // 更新分類篩選器的第一個選項
        const categoryFilter = document.getElementById('presetCategoryFilter');
        if (categoryFilter && categoryFilter.options.length > 0) {
            const allCategoriesText = window.LanguageManager.getText('all_categories');
            if (allCategoriesText && allCategoriesText !== 'all_categories') {
                categoryFilter.options[0].textContent = allCategoriesText;
            }
        }
    }

    /**
     * 隱藏預設項目模態框（純 CSS 版本）
     */
    hidePresetModal() {
        const modalEl = document.getElementById('presetModal');
        if (modalEl) {
            modalEl.classList.remove('is-open');
            modalEl.setAttribute('aria-hidden', 'true');
            document.body.classList.remove('kms-no-scroll');
            if (modalEl._kmsEscHandler) {
                document.removeEventListener('keydown', modalEl._kmsEscHandler);
                delete modalEl._kmsEscHandler;
            }
            if (modalEl._kmsBackdropHandler) {
                modalEl.removeEventListener('click', modalEl._kmsBackdropHandler);
                delete modalEl._kmsBackdropHandler;
            }
        }
    }

    /**
     * 保存順序到資料庫
     */
    async saveOrderToDatabase() {
        try {
            // 準備順序數據
            const orderData = this.pcParts.map((item, index) => ({
                id: item.id,
                sort_order: index + 1
            }));

            const response = await fetch('php/update_pc_parts_order.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ items: orderData })
            });

            const data = await response.json();
            if (!data.success) {
                console.error('Failed to save order:', data.message);
                showMessage('保存順序失敗', 'error');
            }
        } catch (error) {
            console.error('Error saving order:', error);
            showMessage('保存順序時發生錯誤', 'error');
        }
    }

    bindDiscountAutoCalc() {
        const originalEl = document.getElementById('presetItemOriginalPrice');
        const specialEl = document.getElementById('presetItemSpecialPrice');
        const discountEl = document.getElementById('presetItemDiscountPercent');
        if (!originalEl || !specialEl || !discountEl) return;

        const recalc = () => {
            const original = parseFloat(originalEl.value);
            const special = parseFloat(specialEl.value);
            if (!isNaN(original) && original > 0 && !isNaN(special) && special >= 0) {
                const pct = Math.max(0, Math.min(100, Math.round((1 - (special / original)) * 100)));
                discountEl.value = pct;
            } else {
                discountEl.value = '';
            }
        };
        originalEl.removeEventListener('input', recalc);
        specialEl.removeEventListener('input', recalc);
        originalEl.addEventListener('input', recalc);
        specialEl.addEventListener('input', recalc);
    }
}

// 創建全局實例
const presetManager = new PresetManager();

// 導出到全局作用域
window.presetManager = presetManager;

// 確保在 DOM 加載完成後初始化
document.addEventListener('DOMContentLoaded', function() {
    // 等待其他模塊加載完成
    setTimeout(() => {
        console.log('PresetManager initialized');
        console.log('Available functions check:', {
            addItemToReceipt: typeof window.addItemToReceipt,
            ItemManager: typeof window.ItemManager,
            showMessage: typeof window.showMessage
        });
    }, 100);
});

// 導出函數到全局作用域
window.showPresetModal = () => presetManager.showPresetModal();
window.showAddPresetForm = () => presetManager.showAddPresetForm();
window.cancelAddPreset = () => presetManager.cancelAddPreset();
window.savePresetItem = () => presetManager.savePresetItem();
window.editPresetItem = (id) => presetManager.editPresetItem(id);
window.deletePresetItemWithoutConfirm = (id) => presetManager.deletePresetItemWithoutConfirm(id);
window.movePresetItem = (id, direction) => presetManager.movePresetItem(id, direction);
window.changePresetOrder = (id, position) => presetManager.changePresetOrder(id, position);
// 提供給搜尋與分類篩選輸入框呼叫
window.filterPresets = () => presetManager.loadPresetItems();
// 提供關閉模態框（純 CSS）
window.hidePresetModal = () => presetManager.hidePresetModal();
