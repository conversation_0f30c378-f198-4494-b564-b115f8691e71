<?php
/**
 * 配置管理類別
 * KMS PC Receipt Maker
 */

require_once 'DatabaseMySQLi.php';
require_once 'Response.php';

class ConfigurationManager {
    private $db;

    public function __construct() {
        $this->db = new Database();
        $this->initializeTable();
    }

    /**
     * 初始化配置表格
     */
    private function initializeTable() {
        $sql = "CREATE TABLE IF NOT EXISTS receipt_configurations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            customer_name VARCHAR(255),
            customer_phone VARCHAR(50),
            customer_email VARCHAR(255),
            customer_address TEXT,
            payment_method VARCHAR(50),
            items JSON,
            discount_amount DECIMAL(10,2) DEFAULT 0.00,
            tax_rate DECIMAL(5,2) DEFAULT 0.00,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_name (name),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        $this->db->execute($sql);
    }

    /**
     * 保存配置
     */
    public function saveConfiguration($data) {
        $sql = "INSERT INTO receipt_configurations
                (name, customer_name, customer_phone, customer_email, customer_address,
                 payment_method, items, discount_amount, tax_rate, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $params = [
            $data['name'],
            $data['customerInfo']['name'] ?? '',
            $data['customerInfo']['phone'] ?? '',
            $data['customerInfo']['email'] ?? '',
            $data['customerInfo']['address'] ?? '',
            $data['paymentMethod'] ?? 'Cash',
            json_encode($data['items'] ?? []),
            $data['discountAmount'] ?? 0,
            $data['taxRate'] ?? 0,
            $data['notes'] ?? ''
        ];

        return $this->db->execute($sql, $params);
    }

    /**
     * 獲取所有配置
     */
    public function getAllConfigurations() {
        $sql = "SELECT id, name, customer_name, customer_phone, customer_email,
                       customer_address, payment_method, items, discount_amount,
                       tax_rate, notes, created_at, updated_at
                FROM receipt_configurations
                ORDER BY created_at DESC";

        $configurations = $this->db->fetchAll($sql);
        
        // 解析 JSON 項目
        foreach ($configurations as &$config) {
            $config['items'] = json_decode($config['items'], true) ?? [];
            $config['customerInfo'] = [
                'name' => $config['customer_name'],
                'phone' => $config['customer_phone'],
                'email' => $config['customer_email'],
                'address' => $config['customer_address']
            ];
        }

        return $configurations;
    }

    /**
     * 根據 ID 獲取配置
     */
    public function getConfigurationById($id) {
        $sql = "SELECT id, name, customer_name, customer_phone, customer_email,
                       customer_address, payment_method, items, discount_amount,
                       tax_rate, notes, created_at, updated_at
                FROM receipt_configurations
                WHERE id = ?";

        $config = $this->db->fetchOne($sql, [$id]);
        
        if ($config) {
            $config['items'] = json_decode($config['items'], true) ?? [];
            $config['customerInfo'] = [
                'name' => $config['customer_name'],
                'phone' => $config['customer_phone'],
                'email' => $config['customer_email'],
                'address' => $config['customer_address']
            ];
        }

        return $config;
    }

    /**
     * 更新配置
     */
    public function updateConfiguration($id, $data) {
        $sql = "UPDATE receipt_configurations
                SET name = ?, customer_name = ?, customer_phone = ?, customer_email = ?,
                    customer_address = ?, payment_method = ?, items = ?,
                    discount_amount = ?, tax_rate = ?, notes = ?
                WHERE id = ?";

        $params = [
            $data['name'],
            $data['customerInfo']['name'] ?? '',
            $data['customerInfo']['phone'] ?? '',
            $data['customerInfo']['email'] ?? '',
            $data['customerInfo']['address'] ?? '',
            $data['paymentMethod'] ?? 'Cash',
            json_encode($data['items'] ?? []),
            $data['discountAmount'] ?? 0,
            $data['taxRate'] ?? 0,
            $data['notes'] ?? '',
            $id
        ];

        return $this->db->execute($sql, $params);
    }

    /**
     * 刪除配置
     */
    public function deleteConfiguration($id) {
        $sql = "DELETE FROM receipt_configurations WHERE id = ?";
        return $this->db->execute($sql, [$id]);
    }

    /**
     * 檢查配置名稱是否存在
     */
    public function configurationNameExists($name, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM receipt_configurations WHERE name = ?";
        $params = [$name];

        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }

        $result = $this->db->fetchOne($sql, $params);
        return $result['count'] > 0;
    }
}
?>
