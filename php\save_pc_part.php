<?php
/**
 * 新增/更新 電腦零件（預設項目）
 * KMS PC Receipt Maker
 */

require_once 'DatabaseMySQLi.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

try {
    $input = json_decode(file_get_contents('php://input'), true);
    if (!is_array($input)) {
        Response::error('無效的請求內容');
    }

    // 必填: name, category
    $name = isset($input['name']) ? trim($input['name']) : '';
    $category = isset($input['category']) ? trim($input['category']) : '';
    if ($name === '' || $category === '') {
        Response::error('缺少必要欄位 name 或 category');
    }

    $description = isset($input['description']) ? trim($input['description']) : null;
    // default_price 欄位為 NOT NULL，若未提供則使用 0.00
    $default_price = isset($input['default_price']) && $input['default_price'] !== '' ? floatval($input['default_price']) : 0.00;
    $original_price = isset($input['original_price']) && $input['original_price'] !== '' ? floatval($input['original_price']) : null;
    $special_price = isset($input['special_price']) && $input['special_price'] !== '' ? floatval($input['special_price']) : null;

    $db = new Database();

    // 更新
    if (!empty($input['id'])) {
        $id = intval($input['id']);
        // 檢查存在
        $exists = $db->fetch('SELECT id FROM pc_parts WHERE id = ?', [$id]);
        if (!$exists) {
            Response::error('預設項目不存在');
        }

        $sql = 'UPDATE pc_parts SET name = ?, category = ?, description = ?, default_price = ?, original_price = ?, special_price = ? WHERE id = ?';
        $params = [
            $name,
            $category,
            $description,
            $default_price,
            $original_price,
            $special_price,
            $id
        ];
        $db->update($sql, $params);

        Response::success(['id' => $id], '預設項目更新成功');
    }

    // 插入: sort_order = MAX(sort_order)+1
    $max = $db->fetch('SELECT MAX(sort_order) AS max_order FROM pc_parts');
    $nextOrder = isset($max['max_order']) && $max['max_order'] !== null ? (intval($max['max_order']) + 1) : 1;

    $sql = 'INSERT INTO pc_parts (name, category, description, default_price, original_price, special_price, sort_order, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, 1)';
    $params = [
        $name,
        $category,
        $description,
        $default_price,
        $original_price,
        $special_price,
        $nextOrder
    ];

    $newId = $db->insert($sql, $params);
    Response::success(['id' => $newId], '預設項目新增成功');

} catch (Exception $e) {
    Response::error('保存預設項目失敗: ' . $e->getMessage());
}
