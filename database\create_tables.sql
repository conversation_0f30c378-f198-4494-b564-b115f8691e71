-- =====================================================
-- KMS Receipt Maker - Complete Database Setup
-- Complete Database Setup Script v2.0
-- =====================================================
--
-- This script creates all required database tables and indexes
-- Includes user management, receipt management, configuration management functions
--
-- Usage:
-- 1. Execute this script in MySQL
-- 2. Or use reset-database.php for automatic execution
--
-- Default admin account:
-- Username: admin
-- Password: password
-- =====================================================

-- Set SQL mode and character set
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- Create database
CREATE DATABASE IF NOT EXISTS `kms_receipt_maker` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `kms_receipt_maker`;

-- =====================================================
-- Users table (users)
-- Store system user information, including administrators and regular members
-- =====================================================

DROP TABLE IF EXISTS `users`;

CREATE TABLE `users` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'User ID',
    `username` varchar(50) NOT NULL COMMENT 'Username',
    `email` varchar(100) NOT NULL COMMENT 'Email address',
    `password` varchar(255) NOT NULL COMMENT 'Password hash',
    `user_type` enum('admin','member') NOT NULL DEFAULT 'member' COMMENT 'User type: admin=administrator, member=member',
    `status` enum('pending','active','inactive') NOT NULL DEFAULT 'pending' COMMENT 'Account status: pending=pending approval, active=active, inactive=disabled',
    `first_name` varchar(50) DEFAULT NULL COMMENT 'First name',
    `last_name` varchar(50) DEFAULT NULL COMMENT 'Last name',
    `phone` varchar(20) DEFAULT NULL COMMENT 'Phone number',
    `reset_token` varchar(255) DEFAULT NULL COMMENT 'Password reset token',
    `reset_token_expires` datetime DEFAULT NULL COMMENT 'Reset token expiration time',
    `last_login` datetime DEFAULT NULL COMMENT 'Last login time',
    `login_attempts` int(11) NOT NULL DEFAULT 0 COMMENT 'Login attempt count',
    `locked_until` datetime DEFAULT NULL COMMENT 'Account locked until time',
    `email_verified` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Email verified status',
    `email_verify_token` varchar(255) DEFAULT NULL COMMENT 'Email verification token',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Created time',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated time',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_email` (`email`),
    KEY `idx_user_type` (`user_type`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_last_login` (`last_login`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Users table';

-- =====================================================
-- Receipts main table (receipts)
-- Store basic receipt information and total amounts
-- =====================================================

DROP TABLE IF EXISTS `receipts`;

CREATE TABLE `receipts` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Receipt ID',
    `user_id` int(11) DEFAULT NULL COMMENT 'Creator user ID',
    `receipt_number` varchar(50) NOT NULL COMMENT 'Receipt number',
    `customer_name` varchar(100) NOT NULL COMMENT 'Customer name',
    `customer_phone` varchar(20) DEFAULT NULL COMMENT 'Customer phone',
    `customer_email` varchar(100) DEFAULT NULL COMMENT 'Customer email',
    `customer_address` text DEFAULT NULL COMMENT 'Customer address',
    `company_name` varchar(100) DEFAULT NULL COMMENT 'Customer company name',
    `tax_id` varchar(50) DEFAULT NULL COMMENT 'Tax ID number',
    `subtotal` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT 'Subtotal amount',
    `tax_amount` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT 'Tax amount',
    `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT 'Tax rate (%)',
    `discount_amount` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT 'Discount amount',
    `discount_rate` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT 'Discount rate (%)',
    `total_amount` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT 'Total amount',
    `payment_method` enum('Cash','Venmo','Zelle','Square','Stripe','PayPal') NOT NULL DEFAULT 'Cash' COMMENT 'Payment method',
    `payment_status` enum('Pending','Paid','Partial','Cancelled','Refunded') NOT NULL DEFAULT 'Pending' COMMENT 'Payment status',
    `currency` varchar(3) NOT NULL DEFAULT 'USD' COMMENT 'Currency code',
    `exchange_rate` decimal(10,4) NOT NULL DEFAULT 1.0000 COMMENT 'Exchange rate',
    `notes` text DEFAULT NULL COMMENT 'Notes',
    `internal_notes` text DEFAULT NULL COMMENT 'Internal notes',
    `receipt_date` date NOT NULL COMMENT 'Receipt date',
    `due_date` date DEFAULT NULL COMMENT 'Due date',
    `status` enum('Draft','Sent','Paid','Cancelled') NOT NULL DEFAULT 'Draft' COMMENT 'Receipt status',
    `template_id` int(11) DEFAULT NULL COMMENT 'Template ID used',
    `pdf_path` varchar(255) DEFAULT NULL COMMENT 'PDF file path',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Created time',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated time',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_receipt_number` (`receipt_number`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_customer_name` (`customer_name`),
    KEY `idx_receipt_date` (`receipt_date`),
    KEY `idx_status` (`status`),
    KEY `idx_payment_status` (`payment_status`),
    KEY `idx_created_at` (`created_at`),

    CONSTRAINT `fk_receipts_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Receipts main table';

-- =====================================================
-- Receipt items table (receipt_items)
-- Store specific item/product information in receipts
-- =====================================================

DROP TABLE IF EXISTS `receipt_items`;

CREATE TABLE `receipt_items` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Item ID',
    `receipt_id` int(11) NOT NULL COMMENT 'Receipt ID',
    `item_name` varchar(200) NOT NULL COMMENT 'Item name',
    `item_code` varchar(50) DEFAULT NULL COMMENT 'Item code/SKU',
    `category` varchar(50) DEFAULT NULL COMMENT 'Item category',
    `description` text DEFAULT NULL COMMENT 'Item description',
    `quantity` decimal(10,3) NOT NULL DEFAULT 1.000 COMMENT 'Quantity',
    `unit` varchar(20) NOT NULL DEFAULT 'pcs' COMMENT 'Unit',
    `unit_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT 'Unit price',
    `original_price` decimal(12,2) DEFAULT NULL COMMENT 'Original price',
    `special_price` decimal(12,2) DEFAULT NULL COMMENT 'Special price',
    `discount_amount` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT 'Item discount amount',
    `discount_rate` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT 'Item discount rate (%)',
    `discount_percent` int(11) NOT NULL DEFAULT 0 COMMENT 'Discount percentage',
    `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT 'Item tax rate (%)',
    `tax_amount` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT 'Item tax amount',
    `line_total` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT 'Line total',
    `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT 'Sort order',
    `hide_price` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Hide price flag',
    `is_service` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Service item flag',
    `warranty_period` int(11) DEFAULT NULL COMMENT 'Warranty period (days)',
    `notes` text DEFAULT NULL COMMENT 'Item notes',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Created time',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated time',

    PRIMARY KEY (`id`),
    KEY `idx_receipt_id` (`receipt_id`),
    KEY `idx_category` (`category`),
    KEY `idx_item_code` (`item_code`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_original_price` (`original_price`),
    KEY `idx_special_price` (`special_price`),
    KEY `idx_discount_percent` (`discount_percent`),

    CONSTRAINT `fk_receipt_items_receipt_id` FOREIGN KEY (`receipt_id`) REFERENCES `receipts` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Receipt items table';

-- =====================================================
-- Receipt configurations table (receipt_configurations)
-- Store user-saved receipt templates and preset configurations
-- =====================================================

DROP TABLE IF EXISTS `receipt_configurations`;

CREATE TABLE `receipt_configurations` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Configuration ID',
    `user_id` int(11) DEFAULT NULL COMMENT 'User ID',
    `name` varchar(255) NOT NULL COMMENT 'Configuration name',
    `description` text DEFAULT NULL COMMENT 'Configuration description',
    `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Default configuration flag',
    `is_public` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Public template flag',
    `category` varchar(50) DEFAULT NULL COMMENT 'Configuration category',

    -- Customer information presets
    `customer_name` varchar(255) DEFAULT NULL COMMENT 'Default customer name',
    `customer_phone` varchar(50) DEFAULT NULL COMMENT 'Default customer phone',
    `customer_email` varchar(255) DEFAULT NULL COMMENT 'Default customer email',
    `customer_address` text DEFAULT NULL COMMENT 'Default customer address',
    `company_name` varchar(100) DEFAULT NULL COMMENT 'Default company name',
    `tax_id` varchar(50) DEFAULT NULL COMMENT 'Default tax ID',

    -- Payment and tax settings
    `payment_method` varchar(50) DEFAULT 'Cash' COMMENT 'Default payment method',
    `currency` varchar(3) DEFAULT 'USD' COMMENT 'Default currency',
    `tax_rate` decimal(5,2) DEFAULT 0.00 COMMENT 'Default tax rate (%)',
    `discount_rate` decimal(5,2) DEFAULT 0.00 COMMENT 'Default discount rate (%)',

    -- Item configuration (JSON format)
    `items` json DEFAULT NULL COMMENT 'Default item list (JSON format)',
    `default_items` json DEFAULT NULL COMMENT 'Common item library (JSON format)',

    -- Appearance settings
    `template_style` varchar(50) DEFAULT 'default' COMMENT 'Template style',
    `color_scheme` varchar(50) DEFAULT 'blue' COMMENT 'Color scheme',
    `logo_path` varchar(255) DEFAULT NULL COMMENT 'Logo path',
    `header_text` text DEFAULT NULL COMMENT 'Header text',
    `footer_text` text DEFAULT NULL COMMENT 'Footer text',

    -- Other settings
    `notes` text DEFAULT NULL COMMENT 'Notes',
    `settings` json DEFAULT NULL COMMENT 'Other settings (JSON format)',
    `usage_count` int(11) NOT NULL DEFAULT 0 COMMENT 'Usage count',
    `last_used` datetime DEFAULT NULL COMMENT 'Last used time',

    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Created time',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated time',

    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_name` (`name`),
    KEY `idx_category` (`category`),
    KEY `idx_is_default` (`is_default`),
    KEY `idx_is_public` (`is_public`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_usage_count` (`usage_count`),

    CONSTRAINT `fk_receipt_configurations_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Receipt configurations table';

-- =====================================================
-- User activities log table (user_activities)
-- Record various user operations and activities in the system
-- =====================================================

DROP TABLE IF EXISTS `user_activities`;

CREATE TABLE `user_activities` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Log ID',
    `user_id` int(11) DEFAULT NULL COMMENT 'User ID',
    `session_id` varchar(128) DEFAULT NULL COMMENT 'Session ID',
    `activity_type` varchar(50) NOT NULL COMMENT 'Activity type',
    `activity_category` varchar(30) NOT NULL DEFAULT 'general' COMMENT 'Activity category',
    `description` text DEFAULT NULL COMMENT 'Activity description',
    `details` json DEFAULT NULL COMMENT 'Detailed information (JSON format)',
    `target_type` varchar(50) DEFAULT NULL COMMENT 'Target type (e.g. receipt, user)',
    `target_id` int(11) DEFAULT NULL COMMENT 'Target ID',
    `result` enum('success','failure','error') NOT NULL DEFAULT 'success' COMMENT 'Operation result',
    `error_message` text DEFAULT NULL COMMENT 'Error message',
    `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP address',
    `user_agent` text DEFAULT NULL COMMENT 'User agent',
    `referer` varchar(500) DEFAULT NULL COMMENT 'Referer page',
    `request_method` varchar(10) DEFAULT NULL COMMENT 'Request method',
    `request_uri` varchar(500) DEFAULT NULL COMMENT 'Request URI',
    `response_time` decimal(8,3) DEFAULT NULL COMMENT 'Response time (seconds)',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Created time',

    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_session_id` (`session_id`),
    KEY `idx_activity_type` (`activity_type`),
    KEY `idx_activity_category` (`activity_category`),
    KEY `idx_target` (`target_type`, `target_id`),
    KEY `idx_result` (`result`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_ip_address` (`ip_address`),

    CONSTRAINT `fk_user_activities_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User activities log table';

-- =====================================================
-- User sessions table (user_sessions)
-- Store user session information for advanced session management
-- =====================================================

DROP TABLE IF EXISTS `user_sessions`;

CREATE TABLE `user_sessions` (
    `id` varchar(128) NOT NULL COMMENT 'Session ID',
    `user_id` int(11) NOT NULL COMMENT 'User ID',
    `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP address',
    `user_agent` text DEFAULT NULL COMMENT 'User agent',
    `last_activity` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last activity time',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Created time',

    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_last_activity` (`last_activity`),

    CONSTRAINT `fk_user_sessions_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User sessions table';

-- =====================================================
-- User activity logs table (user_activity_logs)
-- Store detailed user activity logs for auditing purposes
-- =====================================================

DROP TABLE IF EXISTS `user_activity_logs`;

CREATE TABLE `user_activity_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Log ID',
    `user_id` int(11) NOT NULL COMMENT 'User ID',
    `action` varchar(100) NOT NULL COMMENT 'Action performed',
    `description` text DEFAULT NULL COMMENT 'Action description',
    `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP address',
    `user_agent` text DEFAULT NULL COMMENT 'User agent',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Created time',

    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_action` (`action`),
    KEY `idx_created_at` (`created_at`),

    CONSTRAINT `fk_user_activity_logs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User activity logs table';

-- =====================================================
-- System settings table (system_settings)
-- Store system global configurations and settings
-- =====================================================

DROP TABLE IF EXISTS `system_settings`;

CREATE TABLE `system_settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Setting ID',
    `setting_key` varchar(100) NOT NULL COMMENT 'Setting key name',
    `setting_value` text DEFAULT NULL COMMENT 'Setting value',
    `setting_type` enum('string','integer','float','boolean','json','text') NOT NULL DEFAULT 'string' COMMENT 'Setting type',
    `category` varchar(50) NOT NULL DEFAULT 'general' COMMENT 'Setting category',
    `description` text DEFAULT NULL COMMENT 'Setting description',
    `is_public` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Is public setting',
    `is_editable` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Is editable',
    `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT 'Sort order',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Created time',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated time',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_setting_key` (`setting_key`),
    KEY `idx_category` (`category`),
    KEY `idx_is_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='System settings table';

-- =====================================================
-- Item categories table (item_categories)
-- Manage categories for receipt items
-- =====================================================

DROP TABLE IF EXISTS `item_categories`;

CREATE TABLE `item_categories` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Category ID',
    `name` varchar(100) NOT NULL COMMENT 'Category name',
    `name_en` varchar(100) DEFAULT NULL COMMENT 'English name',
    `description` text DEFAULT NULL COMMENT 'Category description',
    `parent_id` int(11) DEFAULT NULL COMMENT 'Parent category ID',
    `level` int(11) NOT NULL DEFAULT 1 COMMENT 'Category level',
    `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT 'Sort order',
    `icon` varchar(50) DEFAULT NULL COMMENT 'Icon',
    `color` varchar(7) DEFAULT NULL COMMENT 'Color code',
    `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Is active',
    `created_by` int(11) DEFAULT NULL COMMENT 'Creator ID',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Created time',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated time',

    PRIMARY KEY (`id`),
    KEY `idx_name` (`name`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_level` (`level`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_is_active` (`is_active`),
    KEY `idx_created_by` (`created_by`),

    CONSTRAINT `fk_item_categories_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `item_categories` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk_item_categories_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Item categories table';

-- =====================================================
-- PC Parts table (pc_parts)
-- Store predefined PC components and hardware
-- =====================================================

DROP TABLE IF EXISTS `pc_parts`;

CREATE TABLE `pc_parts` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Part ID',
    `name` varchar(200) NOT NULL COMMENT 'Part name',
    `category` varchar(50) NOT NULL COMMENT 'Part category',
    `category_id` int(11) DEFAULT NULL COMMENT 'Category ID reference',
    `description` text DEFAULT NULL COMMENT 'Part description',
    `default_price` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT 'Default price (for compatibility)',
    `original_price` decimal(12,2) DEFAULT NULL COMMENT 'Original price',
    `special_price` decimal(12,2) DEFAULT NULL COMMENT 'Special price',
    `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Is active',
    `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT 'Sort order',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Created time',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated time',

    PRIMARY KEY (`id`),
    KEY `idx_category` (`category`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_name` (`name`),
    KEY `idx_is_active` (`is_active`),
    KEY `idx_sort_order` (`sort_order`),

    CONSTRAINT `fk_pc_parts_category_id` FOREIGN KEY (`category_id`) REFERENCES `item_categories` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='PC Parts table';

-- =====================================================
-- Initial data insertion
-- =====================================================

-- Create default admin account
INSERT INTO `users` (`username`, `email`, `password`, `user_type`, `status`, `first_name`, `last_name`, `phone`, `email_verified`)
VALUES ('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'active', 'System', 'Administrator', '************', 1)
ON DUPLICATE KEY UPDATE `updated_at` = CURRENT_TIMESTAMP;

-- Insert system settings
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `setting_type`, `category`, `description`, `is_public`) VALUES
('site_name', 'KMS Receipt Maker', 'string', 'general', 'KelvinKMS', 1),
('site_description', 'Professional Receipt Generation System', 'string', 'general', 'Site description', 1),
('default_currency', 'USD', 'string', 'financial', 'Default currency', 1),
('default_tax_rate', '0.00', 'float', 'financial', 'Default tax rate (%)', 1),
('max_login_attempts', '3', 'integer', 'security', 'Maximum login attempts', 0),
('session_timeout', '3600', 'integer', 'security', 'Session timeout (seconds)', 0),
('enable_email_verification', '1', 'boolean', 'email', 'Enable email verification', 0),
('enable_user_registration', '1', 'boolean', 'user', 'Allow user registration', 1),
('require_admin_approval', '1', 'boolean', 'user', 'Require admin approval', 1),
('receipt_number_prefix', 'RCP', 'string', 'receipt', 'Receipt number prefix', 1),
('receipt_number_format', 'RCP-{YYYY}-{MM}-{NNNN}', 'string', 'receipt', 'Receipt number format', 1),
('default_payment_method', 'Cash', 'string', 'receipt', 'Default payment method', 1),
('enable_pdf_generation', '1', 'boolean', 'receipt', 'Enable PDF generation', 1),
('pdf_template', 'default', 'string', 'receipt', 'PDF template', 1),
('company_name', 'Your Company Name', 'string', 'company', 'Company name', 1),
('company_address', 'Your Company Address', 'text', 'company', 'Company address', 1),
('company_phone', 'Your Phone Number', 'string', 'company', 'Company phone', 1),
('company_email', '<EMAIL>', 'string', 'company', 'Company email', 1),
('company_tax_id', 'Your Tax ID', 'string', 'company', 'Company tax ID', 1)
ON DUPLICATE KEY UPDATE `updated_at` = CURRENT_TIMESTAMP;

-- Insert default item categories
-- First insert parent categories
INSERT INTO `item_categories` (`name`, `name_en`, `description`, `parent_id`, `level`, `sort_order`, `icon`, `color`, `created_by`) VALUES
('Computer Hardware', 'Computer Hardware', 'PC components and hardware', NULL, 0, 1, '💻', '#007bff', 1),
('Services', 'Services', 'Installation and maintenance services', NULL, 0, 2, '🔧', '#343a40', 1)
ON DUPLICATE KEY UPDATE `updated_at` = CURRENT_TIMESTAMP;

-- Insert child categories under Computer Hardware (按照指定順序)
INSERT INTO `item_categories` (`name`, `name_en`, `description`, `parent_id`, `level`, `sort_order`, `icon`, `color`, `created_by`) VALUES
('PC Case', 'PC Case', 'Computer cases', 1, 1, 1, '🏠', '#28a745', 1),
('CPU', 'CPU', 'Computer processors', 1, 1, 2, '⚡', '#ffc107', 1),
('CPU Cooler', 'CPU Cooler', 'CPU coolers', 1, 1, 3, '❄️', '#6c757d', 1),
('GPU', 'GPU', 'Graphics cards', 1, 1, 4, '🎮', '#e83e8c', 1),
('RAM', 'RAM', 'Computer memory modules (RAM)', 1, 1, 5, '🧠', '#6f42c1', 1),
('SSD', 'SSD', 'Solid State Drives', 1, 1, 6, '💾', '#fd7e14', 1),
('Motherboard', 'Motherboard', 'Computer motherboards', 1, 1, 7, '🔌', '#17a2b8', 1),
('PSU', 'PSU', 'Power supply units (PSU)', 1, 1, 8, '🔋', '#20c997', 1),
('MB RGB', 'MB RGB', 'Motherboard RGB lighting accessories', 1, 1, 9, '🌈', '#ff6b6b', 1),
('GPU RGB', 'GPU RGB', 'Graphics card RGB lighting accessories', 1, 1, 10, '✨', '#4ecdc4', 1),
('Fan RGB', 'Fan RGB', 'RGB fans and cooling accessories', 1, 1, 11, '🌪️', '#45b7d1', 1),
('Other', 'Other', 'Other computer accessories and components', 1, 1, 12, '📦', '#95a5a6', 1),
('Services', 'Services', 'Other Services', 1, 1, 13, '🔧', '#343a40', 1)
ON DUPLICATE KEY UPDATE `updated_at` = CURRENT_TIMESTAMP;