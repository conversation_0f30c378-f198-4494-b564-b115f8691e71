<?php
/**
 * PHP環境檢查
 */

echo "PHP Version: " . PHP_VERSION . "\n";
echo "PDO Available: " . (extension_loaded('pdo') ? 'Yes' : 'No') . "\n";
echo "PDO MySQL Available: " . (extension_loaded('pdo_mysql') ? 'Yes' : 'No') . "\n";
echo "MySQLi Available: " . (extension_loaded('mysqli') ? 'Yes' : 'No') . "\n";
echo "MySQL Available: " . (extension_loaded('mysql') ? 'Yes' : 'No') . "\n";

if (extension_loaded('pdo')) {
    echo "Available PDO Drivers: " . implode(', ', PDO::getAvailableDrivers()) . "\n";
}

echo "\nLoaded Extensions:\n";
$extensions = get_loaded_extensions();
sort($extensions);
foreach ($extensions as $ext) {
    if (stripos($ext, 'sql') !== false || stripos($ext, 'pdo') !== false) {
        echo "- $ext\n";
    }
}

// 測試基本MySQL連接
try {
    $pdo = new PDO('mysql:host=localhost', 'root', '');
    echo "\nBasic MySQL connection: Success\n";
    $pdo = null;
} catch (Exception $e) {
    echo "\nBasic MySQL connection: Failed - " . $e->getMessage() . "\n";
}
?>
