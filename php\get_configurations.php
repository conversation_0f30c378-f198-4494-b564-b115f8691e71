<?php
/**
 * 獲取收據配置列表
 * KMS PC Receipt Maker
 */

require_once 'DatabaseMySQLi.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

try {
    $db = new Database();
    
    // 獲取所有配置
    $sql = "SELECT * FROM receipt_configurations ORDER BY updated_at DESC";
    $configurations = $db->fetchAll($sql);
    
    // 處理配置數據
    foreach ($configurations as &$config) {
        // 解析JSON數據
        $config['items'] = json_decode($config['items'], true) ?: [];
        
        // 構建客戶信息對象
        $config['customerInfo'] = [
            'name' => $config['customer_name'],
            'phone' => $config['customer_phone'],
            'email' => $config['customer_email'],
            'address' => $config['customer_address']
        ];
        
        // 移除重複字段
        unset($config['customer_name'], $config['customer_phone'], 
              $config['customer_email'], $config['customer_address']);
    }
    
    Response::success($configurations, '配置列表獲取成功');
    
} catch (Exception $e) {
    error_log('Get configurations error: ' . $e->getMessage());
    Response::error('獲取配置列表失敗: ' . $e->getMessage());
}
?>