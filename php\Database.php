<?php
/**
 * 數據庫類 (文件存儲版本)
 * KMS PC Receipt Maker
 */

require_once 'config.php';

class Database {
    private $dataDir;
    private $error;

    /**
     * 構造函數
     */
    public function __construct() {
        $this->dataDir = __DIR__ . '/../data/';
        
        // 創建數據目錄
        if (!is_dir($this->dataDir)) {
            mkdir($this->dataDir, 0755, true);
        }
        
        // 初始化數據文件
        $this->initializeData();
    }

    /**
     * 初始化數據文件
     */
    private function initializeData() {
        // 初始化收據文件
        if (!file_exists($this->dataDir . 'receipts.json')) {
            file_put_contents($this->dataDir . 'receipts.json', json_encode([]));
        }
        
        // 初始化收據項目文件
        if (!file_exists($this->dataDir . 'receipt_items.json')) {
            file_put_contents($this->dataDir . 'receipt_items.json', json_encode([]));
        }
        
        // 初始化電腦零件文件
        if (!file_exists($this->dataDir . 'pc_parts.json')) {
            $pcParts = [
                ['id' => 1, 'name' => 'Intel Core i5-12400F', 'category' => 'CPU', 'description' => 'Intel 第12代處理器，6核心12線程', 'default_price' => 6500.00, 'is_active' => true],
                ['id' => 2, 'name' => 'Intel Core i7-12700K', 'category' => 'CPU', 'description' => 'Intel 第12代處理器，12核心20線程', 'default_price' => 12000.00, 'is_active' => true],
                ['id' => 3, 'name' => 'AMD Ryzen 5 5600X', 'category' => 'CPU', 'description' => 'AMD Zen3架構，6核心12線程', 'default_price' => 7500.00, 'is_active' => true],
                ['id' => 4, 'name' => 'ASUS PRIME B550M-A', 'category' => 'Motherboard', 'description' => 'AMD B550晶片組，支援Ryzen 5000系列', 'default_price' => 3200.00, 'is_active' => true],
                ['id' => 5, 'name' => 'Corsair Vengeance LPX 16GB DDR4-3200', 'category' => 'Memory', 'description' => '16GB DDR4記憶體，3200MHz', 'default_price' => 2800.00, 'is_active' => true],
                ['id' => 6, 'name' => 'NVIDIA GeForce RTX 3060', 'category' => 'Graphics Card', 'description' => '12GB GDDR6，適合1080p遊戲', 'default_price' => 15000.00, 'is_active' => true],
                ['id' => 7, 'name' => 'Samsung 980 PRO 1TB NVMe SSD', 'category' => 'Storage', 'description' => '1TB NVMe SSD，PCIe 4.0', 'default_price' => 4500.00, 'is_active' => true],
                ['id' => 8, 'name' => 'Corsair RM750x 750W', 'category' => 'Power Supply', 'description' => '750W 80+ Gold認證，全模組化', 'default_price' => 3800.00, 'is_active' => true],
                ['id' => 9, 'name' => 'Fractal Design Define 7', 'category' => 'Case', 'description' => 'ATX靜音機殼，優秀散熱設計', 'default_price' => 4500.00, 'is_active' => true],
                ['id' => 10, 'name' => 'Noctua NH-D15', 'category' => 'Cooler', 'description' => '雙塔風冷散熱器，靜音高效', 'default_price' => 3200.00, 'is_active' => true],

                // 服務項目
                ['id' => 11, 'name' => '電腦組裝服務', 'category' => 'Service', 'description' => '專業電腦組裝，包含系統安裝', 'default_price' => 1500.00, 'is_active' => true],
                ['id' => 12, 'name' => '系統重灌', 'category' => 'Service', 'description' => 'Windows系統重新安裝，包含驅動程式', 'default_price' => 800.00, 'is_active' => true],
                ['id' => 13, 'name' => '硬體檢測', 'category' => 'Service', 'description' => '全面硬體檢測與故障診斷', 'default_price' => 500.00, 'is_active' => true],
                ['id' => 14, 'name' => '資料救援', 'category' => 'Service', 'description' => '硬碟資料救援服務', 'default_price' => 2000.00, 'is_active' => true],
                ['id' => 15, 'name' => '電腦清潔保養', 'category' => 'Service', 'description' => '內部清潔、散熱膏更換', 'default_price' => 600.00, 'is_active' => true]
            ];
            file_put_contents($this->dataDir . 'pc_parts.json', json_encode($pcParts, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
        }
    }

    /**
     * 讀取JSON文件
     */
    private function readJsonFile($filename) {
        $filepath = $this->dataDir . $filename;
        if (!file_exists($filepath)) {
            return [];
        }
        
        $content = file_get_contents($filepath);
        return json_decode($content, true) ?: [];
    }

    /**
     * 寫入JSON文件
     */
    private function writeJsonFile($filename, $data) {
        $filepath = $this->dataDir . $filename;
        return file_put_contents($filepath, json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
    }

    /**
     * 獲取下一個ID
     */
    private function getNextId($data) {
        if (empty($data)) {
            return 1;
        }
        
        $maxId = 0;
        foreach ($data as $item) {
            if (isset($item['id']) && $item['id'] > $maxId) {
                $maxId = $item['id'];
            }
        }
        
        return $maxId + 1;
    }

    /**
     * 模擬fetch方法
     */
    public function fetch($sql, $params = []) {
        // 簡化的查詢解析
        if (strpos($sql, 'pc_parts') !== false && strpos($sql, 'COUNT') !== false) {
            $pcParts = $this->readJsonFile('pc_parts.json');
            return ['count' => count($pcParts)];
        }
        
        return null;
    }

    /**
     * 模擬fetchAll方法
     */
    public function fetchAll($sql, $params = []) {
        if (strpos($sql, 'pc_parts') !== false) {
            return $this->readJsonFile('pc_parts.json');
        }
        
        if (strpos($sql, 'receipts') !== false) {
            return $this->readJsonFile('receipts.json');
        }
        
        return [];
    }

    /**
     * 模擬insert方法
     */
    public function insert($sql, $params = []) {
        if (strpos($sql, 'receipts') !== false && strpos($sql, 'INSERT') !== false) {
            $receipts = $this->readJsonFile('receipts.json');
            $newId = $this->getNextId($receipts);
            
            $receipt = [
                'id' => $newId,
                'receipt_number' => $params[0],
                'customer_name' => $params[1],
                'customer_phone' => $params[2],
                'customer_email' => $params[3],
                'customer_address' => $params[4],
                'subtotal' => $params[5],
                'tax_amount' => $params[6],
                'discount_amount' => $params[7],
                'total_amount' => $params[8],
                'payment_method' => $params[9],
                'notes' => $params[10],
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $receipts[] = $receipt;
            $this->writeJsonFile('receipts.json', $receipts);
            
            return $newId;
        }
        
        if (strpos($sql, 'receipt_items') !== false && strpos($sql, 'INSERT') !== false) {
            $items = $this->readJsonFile('receipt_items.json');
            $newId = $this->getNextId($items);
            
            $item = [
                'id' => $newId,
                'receipt_id' => $params[0],
                'item_name' => $params[1],
                'item_description' => $params[2],
                'category' => $params[3],
                'quantity' => $params[4],
                'unit_price' => $params[5],
                'total_price' => $params[6],
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $items[] = $item;
            $this->writeJsonFile('receipt_items.json', $items);
            
            return $newId;
        }
        
        return 0;
    }

    /**
     * 檢查連接狀態
     */
    public function isConnected() {
        return is_dir($this->dataDir) && is_writable($this->dataDir);
    }

    /**
     * 獲取錯誤信息
     */
    public function getError() {
        return $this->error;
    }

    /**
     * 開始事務 (文件數據庫中不需要)
     */
    public function beginTransaction() {
        return true;
    }

    /**
     * 提交事務
     */
    public function commit() {
        return true;
    }

    /**
     * 回滾事務
     */
    public function rollback() {
        return true;
    }

    /**
     * 關閉連接
     */
    public function close() {
        // 文件數據庫不需要關閉連接
    }
}
?>
