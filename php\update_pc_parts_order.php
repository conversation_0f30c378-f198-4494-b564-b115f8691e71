<?php
/**
 * 更新電腦零件排序
 * KMS PC Receipt Maker
 */

require_once 'DatabaseMySQLi.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

// 只允許POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Response::error('只允許POST請求', 405);
}

try {
    // 獲取JSON數據
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data || !isset($data['items']) || !is_array($data['items'])) {
        Response::error('無效的請求數據');
    }
    
    $db = new Database();
    $db->beginTransaction();
    
    try {
        // 更新每個項目的排序
        $sql = "UPDATE pc_parts SET sort_order = ? WHERE id = ?";
        
        foreach ($data['items'] as $item) {
            if (!isset($item['id']) || !isset($item['sort_order'])) {
                continue;
            }
            
            $db->update($sql, [
                intval($item['sort_order']),
                intval($item['id'])
            ]);
        }
        
        $db->commit();
        Response::success(null, '排序更新成功');
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log('Update PC parts order error: ' . $e->getMessage());
    Response::error('更新排序失敗: ' . $e->getMessage());
}
?>