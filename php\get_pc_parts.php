<?php
/**
 * 獲取電腦零件列表
 * KMS PC Receipt Maker
 */

require_once 'ReceiptManager.php';

header('Content-Type: application/json; charset=utf-8');

try {
    $receiptManager = new ReceiptManager();
    
    // 獲取分類參數
    $category = isset($_GET['category']) ? trim($_GET['category']) : '';
    
    // 獲取電腦零件列表
    $pcParts = $receiptManager->getPcParts($category);
    
    Response::success($pcParts, '電腦零件列表獲取成功');
    
} catch (Exception $e) {
    Response::error('獲取電腦零件列表失敗: ' . $e->getMessage());
}
?>
