/**
 * Application Initializer
 * Handles app startup, configuration loading, and initial setup
 */

class AppInitializer {
    constructor() {
        this.currentReceiptNumber = '';
        this.companyLogo = null;
    }

    /**
     * Initialize Application
     */
    async init() {
        // Generate receipt number
        this.generateReceiptNumber();

        // Date field removed - no longer needed

        // Initialize event listeners
        this.initializeEventListeners();

        // Load configuration
        await this.loadSystemConfig();

        // UI will be updated by individual modules when they initialize
    }

    /**
     * Generate Receipt Number
     */
    generateReceiptNumber() {
        // Default fixed number format
        this.currentReceiptNumber = 'KMS-UltraVIP-0000001';
        const receiptNumberElement = document.getElementById('receiptNumber');
        if (receiptNumberElement) {
            receiptNumberElement.value = this.currentReceiptNumber;
        }
    }



    /**
     * Initialize Event Listeners
     */
    initializeEventListeners() {
        // Discount and tax rate changes
        const discountElement = document.getElementById('discountAmount');
        if (discountElement) {
            discountElement.addEventListener('input', () => {
                if (typeof ItemManager !== 'undefined') {
                    ItemManager.updateTotals();
                }
            });
        }

        const taxRateElement = document.getElementById('taxRate');
        if (taxRateElement) {
            taxRateElement.addEventListener('input', () => {
                if (typeof ItemManager !== 'undefined') {
                    ItemManager.updateTotals();
                }
            });
        }

        // Logo upload
        const logoUploadElement = document.getElementById('logoUpload');
        if (logoUploadElement) {
            logoUploadElement.addEventListener('change', (event) => {
                this.handleLogoUpload(event);
            });
        }

        // Form submission prevention
        const receiptForm = document.getElementById('receiptForm');
        if (receiptForm) {
            receiptForm.addEventListener('submit', (e) => {
                e.preventDefault();
            });
        }
    }

    /**
     * Load System Configuration
     */
    async loadSystemConfig() {
        try {
            const response = await fetch('php/get_config.php');
            const data = await response.json();

            if (data.success) {
                // Configuration data can be used here
                console.log('System config loaded:', data.data);
                return data.data;
            }
        } catch (error) {
            console.error('Error loading system config:', error);
        }
        return null;
    }

    /**
     * Handle Logo Upload
     */
    handleLogoUpload(event) {
        const file = event.target.files[0];
        if (!file) {
            this.hideLogo();
            return;
        }

        // Check file type
        if (!file.type.startsWith('image/')) {
            alert('Please select a valid image file');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            // Create image object to get dimensions
            const img = new Image();
            img.onload = () => {
                window.currentLogo = {
                    src: e.target.result,
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    width: img.width,
                    height: img.height,
                    x: 20, // Default position
                    y: 20,
                    displayWidth: 100, // Display size
                    displayHeight: 100
                };
                
                // Show preview
                this.showLogoPreview(window.currentLogo, file);
                
                // Show success message
                if (typeof UIManager !== 'undefined' && UIManager.showMessage) {
                    UIManager.showMessage('Logo uploaded successfully!', 'success');
                } else if (typeof showMessage === 'function') {
                    showMessage('Logo uploaded successfully!', 'success');
                } else {
                    console.log('Logo uploaded successfully!');
                }
            };
            img.src = e.target.result;
        };
        
        reader.readAsDataURL(file);
    }

    /**
     * Show Logo Preview
     */
    showLogoPreview(logoData, file) {
        const previewContainer = document.getElementById('logoPreview');
        const previewImage = document.getElementById('logoPreviewImage');
        const fileName = document.getElementById('logoFileName');
        const fileType = document.getElementById('logoFileType');
        const fileSize = document.getElementById('logoFileSize');
        const imageDimensions = document.getElementById('logoImageDimensions');
        
        if (previewImage) previewImage.src = logoData.src;
        if (fileName) fileName.textContent = logoData.name;
        if (fileType) fileType.textContent = logoData.type;
        if (fileSize) fileSize.textContent = this.formatFileSize(logoData.size);
        if (imageDimensions) imageDimensions.textContent = `${logoData.width} × ${logoData.height} px`;
        
        // Show preview container
        if (previewContainer) {
            previewContainer.style.display = 'block';
        }
    }

    /**
     * Format File Size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Remove Logo
     */
    removeLogo() {
        window.currentLogo = null;
        const logoUploadElement = document.getElementById('logoUpload');
        if (logoUploadElement) {
            logoUploadElement.value = '';
        }
        this.hideLogo();
        if (typeof UIManager !== 'undefined' && UIManager.showMessage) {
            UIManager.showMessage('Logo removed', 'success');
        } else if (typeof showMessage === 'function') {
            showMessage('Logo removed', 'success');
        } else {
            console.log('Logo removed');
        }
    }

    /**
     * Hide Logo Preview
     */
    hideLogo() {
        const previewContainer = document.getElementById('logoPreview');
        if (previewContainer) {
            previewContainer.style.display = 'none';
        }
    }

    /**
     * Get current receipt number
     */
    getCurrentReceiptNumber() {
        return this.currentReceiptNumber;
    }
}

// Create global instance
window.AppInitializer = new AppInitializer();

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.AppInitializer.init();
});

// Export functions for backward compatibility
window.generateReceiptNumber = () => window.AppInitializer.generateReceiptNumber();
window.handleLogoUpload = (event) => window.AppInitializer.handleLogoUpload(event);
window.removeLogo = () => window.AppInitializer.removeLogo();
window.formatFileSize = (bytes) => window.AppInitializer.formatFileSize(bytes);
