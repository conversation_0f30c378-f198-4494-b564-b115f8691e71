<?php
/**
 * 批量刪除收據
 * KMS PC Receipt Maker
 */

require_once 'ReceiptManager.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

try {
    // 只允許 POST 請求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        Response::error('只允許 POST 請求', 405);
    }

    // 獲取 JSON 輸入
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['ids']) || !is_array($input['ids'])) {
        Response::error('缺少收據 ID 列表', 400);
    }
    
    $receiptIds = array_map('intval', $input['ids']);
    $receiptIds = array_filter($receiptIds, function($id) {
        return $id > 0;
    });
    
    if (empty($receiptIds)) {
        Response::error('沒有有效的收據 ID', 400);
    }
    
    $receiptManager = new ReceiptManager();
    $result = $receiptManager->deleteReceipts($receiptIds);
    
    if ($result) {
        Response::success([
            'deleted_count' => count($receiptIds)
        ], '收據批量刪除成功');
    } else {
        Response::error('收據批量刪除失敗');
    }
    
} catch (Exception $e) {
    error_log('Delete receipts error: ' . $e->getMessage());
    Response::error('批量刪除收據時發生錯誤: ' . $e->getMessage());
}
?>