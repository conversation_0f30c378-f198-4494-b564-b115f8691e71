<?php
/**
 * 獲取系統配置
 * KMS PC Receipt Maker
 */

require_once 'config.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

try {
    // 系統配置數據
    $config = [
        'company_name' => COMPANY_NAME,
        'company_address' => COMPANY_ADDRESS,
        'company_phone' => COMPANY_PHONE,
        'company_email' => COMPANY_EMAIL,
        'tax_rate' => TAX_RATE,
        'app_name' => APP_NAME,
        'app_version' => APP_VERSION,
        'currency' => 'USD',
        'currency_symbol' => '$',
        'date_format' => 'Y-m-d H:i:s',
        'timezone' => 'US/Pacific'
    ];
    
    Response::success($config, '系統配置獲取成功');
    
} catch (Exception $e) {
    error_log('Get config error: ' . $e->getMessage());
    Response::error('獲取系統配置失敗: ' . $e->getMessage());
}
?>