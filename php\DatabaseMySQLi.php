<?php
/**
 * 數據庫連接類 (使用MySQLi)
 * KMS PC Receipt Maker
 */

require_once 'config.php';

class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    private $connection;
    private $error;

    /**
     * 構造函數 - 建立數據庫連接
     */
    public function __construct() {
        try {
            $this->connection = new mysqli($this->host, $this->username, $this->password, $this->db_name);
            
            if ($this->connection->connect_error) {
                throw new Exception("Connection failed: " . $this->connection->connect_error);
            }
            
            $this->connection->set_charset($this->charset);
            
        } catch (Exception $e) {
            $this->error = $e->getMessage();
            error_log("Database connection error: " . $this->error);
            throw new Exception("數據庫連接失敗");
        }
    }

    /**
     * 獲取連接實例
     */
    public function getConnection() {
        return $this->connection;
    }

    /**
     * 執行查詢
     */
    public function query($sql, $params = []) {
        try {
            if (!empty($params)) {
                $stmt = $this->connection->prepare($sql);
                if (!$stmt) {
                    throw new Exception("Prepare failed: " . $this->connection->error);
                }
                
                $types = $this->getParamTypes($params);
                $stmt->bind_param($types, ...$params);
                
                if (!$stmt->execute()) {
                    throw new Exception("Execute failed: " . $stmt->error);
                }
                
                return $stmt;
            } else {
                $result = $this->connection->query($sql);
                if (!$result) {
                    throw new Exception("Query failed: " . $this->connection->error);
                }
                return $result;
            }
        } catch (Exception $e) {
            error_log("Query error: " . $e->getMessage());
            throw new Exception("查詢執行失敗: " . $e->getMessage());
        }
    }

    /**
     * 獲取單行數據
     */
    public function fetch($sql, $params = []) {
        $result = $this->query($sql, $params);
        
        if ($result instanceof mysqli_stmt) {
            $data = $result->get_result();
            return $data->fetch_assoc();
        } else {
            return $result->fetch_assoc();
        }
    }

    /**
     * 獲取多行數據
     */
    public function fetchAll($sql, $params = []) {
        $result = $this->query($sql, $params);
        $rows = [];
        
        if ($result instanceof mysqli_stmt) {
            $data = $result->get_result();
            while ($row = $data->fetch_assoc()) {
                $rows[] = $row;
            }
        } else {
            while ($row = $result->fetch_assoc()) {
                $rows[] = $row;
            }
        }
        
        return $rows;
    }

    /**
     * 插入數據並返回插入的ID
     */
    public function insert($sql, $params = []) {
        $this->query($sql, $params);
        return $this->connection->insert_id;
    }

    /**
     * 更新數據並返回影響的行數
     */
    public function update($sql, $params = []) {
        $this->query($sql, $params);
        return $this->connection->affected_rows;
    }

    /**
     * 刪除數據並返回影響的行數
     */
    public function delete($sql, $params = []) {
        $this->query($sql, $params);
        return $this->connection->affected_rows;
    }

    /**
     * 開始事務
     */
    public function beginTransaction() {
        return $this->connection->autocommit(false);
    }

    /**
     * 提交事務
     */
    public function commit() {
        $result = $this->connection->commit();
        $this->connection->autocommit(true);
        return $result;
    }

    /**
     * 回滾事務
     */
    public function rollback() {
        $result = $this->connection->rollback();
        $this->connection->autocommit(true);
        return $result;
    }

    /**
     * 執行SQL語句（用於INSERT、UPDATE、DELETE等）
     */
    public function execute($sql, $params = []) {
        try {
            if (!empty($params)) {
                $stmt = $this->connection->prepare($sql);
                if (!$stmt) {
                    throw new Exception("Prepare failed: " . $this->connection->error);
                }

                $types = $this->getParamTypes($params);
                $stmt->bind_param($types, ...$params);

                if (!$stmt->execute()) {
                    throw new Exception("Execute failed: " . $stmt->error);
                }

                return $this->connection->affected_rows;
            } else {
                $result = $this->connection->query($sql);
                if (!$result) {
                    throw new Exception("Query failed: " . $this->connection->error);
                }
                return $this->connection->affected_rows;
            }
        } catch (Exception $e) {
            error_log("Execute error: " . $e->getMessage());
            throw new Exception("執行SQL失敗: " . $e->getMessage());
        }
    }

    /**
     * 獲取單行數據（別名方法）
     */
    public function fetchOne($sql, $params = []) {
        return $this->fetch($sql, $params);
    }

    /**
     * 檢查數據庫連接是否正常
     */
    public function isConnected() {
        return $this->connection && $this->connection->ping();
    }

    /**
     * 獲取錯誤信息
     */
    public function getError() {
        return $this->error;
    }

    /**
     * 關閉數據庫連接
     */
    public function close() {
        if ($this->connection) {
            $this->connection->close();
        }
    }

    /**
     * 獲取參數類型字符串
     */
    private function getParamTypes($params) {
        $types = '';
        foreach ($params as $param) {
            if (is_int($param)) {
                $types .= 'i';
            } elseif (is_float($param)) {
                $types .= 'd';
            } else {
                $types .= 's';
            }
        }
        return $types;
    }

    /**
     * 析構函數
     */
    public function __destruct() {
        $this->close();
    }
}
?>
